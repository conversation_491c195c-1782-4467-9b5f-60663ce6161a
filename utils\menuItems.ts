import { Minus } from "lucide-react";
import {
  HOME_PATH,
  CAR_TICKETS_PATH,
  MOTOR_TICKETS_PATH,
  CAR_VIOLATION_IMAGE_PATH,
  CAR_INSURANCE_PATH,
  DRIVING_LICENSE_STATUS_PATH,
  CAR_ID_DOCUMENTS_PATH,
  DRIVING_LICENSE_POINT_PATH,
  PLATE_HISTORY_PATH,
  CONTACT_US_PATH,
  BLOG_PATH,
  RULES_PATH,
  PRIVACY_POLICY_PATH,
  DASHBOARD_INQUIRY_HISTORY_PATH,
  DASHBOARD_USER_ADDRESSES_PATH,
  DASHBOARD_PATH,
  DASHBOARD_PROFILE_PATH,
  DASHBOARD_ORDER_HISTORY_PATH
} from "@/lib/routes";


interface NavigationLink  {
    title: string;
    href: string;
    type: "mobile" | "both" | "desktop";
    status: string;
  }
  export const getMenuItems = (services_status:  {[key: string]: "ACTIVE" | "DEACTIVE"}): NavigationLink[] => [
  {
    title: "صفحه اصلی",
    href: HOME_PATH,
    type: "both",
     
    status: "ACTIVE"
  },
  {
    title: "داشبورد",
    href: DASHBOARD_PATH,
    type: "mobile",
     
    status: "ACTIVE"
  },
  {
    title: "پروفایل",
    href: DASHBOARD_PROFILE_PATH,
    type: "mobile",
     
    status: "DEACTIVE"
  },
  {
    title: "استعلام خلافی خودرو",
    href: CAR_TICKETS_PATH,
    type: "both",
     
    status: "ACTIVE"
  },
  {
    title: "استعلام خلافی موتور",
    href: MOTOR_TICKETS_PATH,
    type: "both",
    
    status: "ACTIVE"
  },
  {
    title: "تصویر تخلفات رانندگی",
    href: CAR_VIOLATION_IMAGE_PATH,
    type: "mobile",
    
    status: services_status.car_violation_image
  },
  {
    title: "استعلام بیمه شخص ثالث",
    href: CAR_INSURANCE_PATH,
    type: "mobile",
    
    status: services_status?.car_insurance
  },
  {
    title: "وضعیت گواهینامه",
    href: DRIVING_LICENSE_STATUS_PATH,
    type: "mobile",
    
    status: services_status?.driving_license_status
  },
  {
    title: "وضعیت کارت و سند خودرو",
    href: CAR_ID_DOCUMENTS_PATH,
    type: "mobile",
    
    status: services_status?.carid_decuments
  },
  {
    title: "استعلام نمره منفی",
    href: DRIVING_LICENSE_POINT_PATH,
    type: "mobile",
    
    status: services_status?.driving_license_point
  },
  {
    title: "استعلام تاریخچه پلاک",
    href: PLATE_HISTORY_PATH,
    type: "mobile",
    
    status: services_status?.plate_history
  },
  {
    title: "استعلام ها",
    href: DASHBOARD_INQUIRY_HISTORY_PATH,
    type: "mobile",    
    status: "DEACTIVE"
  },
  {
    title: "آدرس های من",
    href: DASHBOARD_USER_ADDRESSES_PATH,
    type: "mobile",    
    status: "DEACTIVE"
  },
  {
    title: "تماس با ما",
    href: CONTACT_US_PATH,
    type: "both",
    
    status: "ACTIVE"
  },
  {
    title: "وبلاگ",
    href: BLOG_PATH,
    type: "both",
    
    status: services_status?.blog
  },
  {
    title: "قوانین و مقررات",
    href: RULES_PATH,
    type: "mobile",
    
    status: "ACTIVE"
  },
  {
    title: "سیاست حریم خصوصی",
    href: PRIVACY_POLICY_PATH,
    type: "mobile",
    
    status: "ACTIVE"
  }
];
export const getDashboardMenuItems = (services_status:  {[key: string]: "ACTIVE" | "DEACTIVE"}): NavigationLink[] => [
  {
    title: "صفحه اصلی",
    href: HOME_PATH,
    type: "both",
     
    status: "ACTIVE"
  },
  {
    title: "داشبورد",
    href: DASHBOARD_PATH,
    type: "mobile",
     
    status: "ACTIVE"
  },
  {
    title: "پروفایل",
    href: DASHBOARD_PROFILE_PATH,
    type: "both",
     
    status: "ACTIVE"
  },
  {
    title: "استعلام خلافی خودرو",
    href: CAR_TICKETS_PATH,
    type: "both",
     
    status: "ACTIVE"
  },
  {
    title: "استعلام خلافی موتور",
    href: MOTOR_TICKETS_PATH,
    type: "both",
    
    status: "ACTIVE"
  },
  {
    title: "تصویر تخلفات رانندگی",
    href: CAR_VIOLATION_IMAGE_PATH,
    type: "mobile",
    
    status: services_status.car_violation_image
  },
  {
    title: "استعلام بیمه شخص ثالث",
    href: CAR_INSURANCE_PATH,
    type: "mobile",
    
    status: services_status?.car_insurance
  },
  {
    title: "وضعیت گواهینامه",
    href: DRIVING_LICENSE_STATUS_PATH,
    type: "mobile",
    
    status: services_status?.driving_license_status
  },
  {
    title: "وضعیت کارت و سند خودرو",
    href: CAR_ID_DOCUMENTS_PATH,
    type: "mobile",
    
    status: services_status?.carid_decuments
  },
  {
    title: "استعلام نمره منفی",
    href: DRIVING_LICENSE_POINT_PATH,
    type: "mobile",
    
    status: services_status?.driving_license_point
  },
  {
    title: "استعلام تاریخچه پلاک",
    href: PLATE_HISTORY_PATH,
    type: "mobile",
    
    status: services_status?.plate_history
  },
  {
    title: "استعلام ها",
    href: DASHBOARD_INQUIRY_HISTORY_PATH,
    type: "both",    
    status: "ACTIVE"
  },
  {
    title: "آدرس های من",
    href: DASHBOARD_USER_ADDRESSES_PATH,
    type: "both",    
    status: "ACTIVE"
  },
  {
    title: "تاریخچه سفارشات",
    href: DASHBOARD_ORDER_HISTORY_PATH,
    type: "both",    
    status: "ACTIVE"
  },
  {
    title: "تماس با ما",
    href: CONTACT_US_PATH,
    type: "both",
    
    status: "ACTIVE"
  },
  {
    title: "وبلاگ",
    href: BLOG_PATH,
    type: "both",
    
    status: services_status?.blog
  },
  {
    title: "قوانین و مقررات",
    href: RULES_PATH,
    type: "mobile",
    
    status: "ACTIVE"
  },
  {
    title: "سیاست حریم خصوصی",
    href: PRIVACY_POLICY_PATH,
    type: "mobile",
    
    status: "ACTIVE"
  }
];