import React from 'react'
import AccordionHeader from './AccordionHeader'

const ConditionalFilter = () => {
    return (
        <div
            className='bg-white max-md:border border-gray-200  max-md:mb-3 min-h-20 rounded-3xl p-3 search-products-filter md:pb-5'>
            <AccordionHeader title="انتخاب شرطی بر اساس">
                <ul className="flex flex-col gap-5  text-base font-light">
                    <div className="flex flex-col gap-7 justify-between mb-2">
                        <div className="custom-checkbox">
                            <label className="flex items-center gap-2 text-sm font-medium">
                                <input type="checkbox" className="" id="iranian_products"/>
                                فقط محصولات ایرانی
                            </label>
                        </div>
                        <div className="custom-checkbox">
                            <label className="flex items-center gap-2 text-sm font-medium">
                                <input type="checkbox" className="" id="foreigner_products"/>
                                فقط محصولات خارجی
                            </label>
                        </div>

                    </div>
                </ul>
            </AccordionHeader>

        </div>
    )
}

export default ConditionalFilter