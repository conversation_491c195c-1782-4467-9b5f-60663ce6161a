@tailwind base;
@tailwind components;
@tailwind utilities;

/* @font-face {
    font-family: 'IRANSansWeb';
    src: url('/assets/fonts/iransans/IRANSansWeb_Bold.woff2') format('woff2');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'IRANSansWeb';
    src: url('/assets/fonts/iransans/IRANSansWeb_Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'IRANSansWeb';
    src: url('/assets/fonts/iransans/IRANSansWeb_Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'IRANSansWeb';
    src: url('/assets/fonts/iransans/IRANSansWeb_UltraLight.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
} */

.custom-container {
    background-image: url('/assets/images/background.svg');
}

.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);
}
* {
    direction: rtl;
}

.borderless-input {
    @apply !border-none focus-visible:!ring-offset-0 focus-visible:!ring-0 focus-visible:!outline-none;
}

.left-direction {
    direction: ltr !important;
}


.login-form {
    background-image: url("/assets/images/fade-logo.png");
    background-repeat: no-repeat;
    background-position-x: 20px;
    background-position-y: 90px;
}

.services-items li {
    width: 24%;
}

.services-items li a {
    text-align: center;
}

.product-help p {
    line-height: 2rem;
    text-align: justify;
}
.product-help ol li {
    margin: 5px 0;
}
.product-help ol {
    margin: 15px 0;
}
.product-help h2 {
    margin-bottom: 12px;
    font-size: 1.3rem;
    font-weight: 800;
}
figure {
    margin: 0 auto;
}
figure img {
    border-radius: 5%;
    width: 100% !important;
}
.article-section h1 {
    font-size: 32px !important;
    font-weight: 700 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h2 {
    font-size: 28px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h3 {
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h4 {
    font-size: 20px !important;
    font-weight: 500 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h5 {
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h6 {
    font-size: 14px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

blockquote {
  background-color: #F5F6F8;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.25rem; /* px-5 */
  padding-right: 1.25rem;
  padding-top: 2rem; /* py-8 */
  padding-bottom: 2rem;
  border-radius: 1.5rem; /* rounded-3xl */
  background-image: url('/assets/images/double-comma.png');
  background-repeat: no-repeat;
  background-size: 70px auto;
  background-position: 90px 40px;
}

@media (max-width: 768px) {
  blockquote {
    width: 96%;
    padding-top: 1.25rem; /* py-5 */
    padding-bottom: 1.25rem;
    background-size: 60px auto;
    background-position: 40px 90px;
  }
}

blockquote h1,
blockquote h2,
blockquote h3,
blockquote h4,
blockquote h5,
blockquote h6 {
  position: relative !important;
  margin-bottom: 1.25rem;
  padding-right: 1.25rem;
  font-weight: 900;
  display: inline-block;
}

blockquote h3::before {
  content: "*";
  color: transparent;
  z-index: 1000;
  position: absolute;
  top: 0.3em;
  right: -4px;
  width: 0.9em;
  height: 0.9em;
  background-color: transparent;
  border: 2px solid #FFD600;
  padding-left: 10px;
  border-radius: 9999px;
}

blockquote p {

  text-align: justify;
  line-height: 2rem; /* leading-8 */
  color:#302e2e !important;
}
.cart-circles-yellow {
    background-image: url("/assets/images/half-circle-yellow.png");
    background-repeat: no-repeat;
}

@media (max-width: 768px) {
  blockquote p {
    line-height: 1.75rem; /* leading-7 */
    color:#302e2e !important;
  }
}

@layer base {
    :root {
        --background: #F5F6F8;
        --foreground: #62676E;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: #1F84FB;
        --primary-foreground: #0A0A0C;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: #F9FAFB;
        --muted-foreground: #9DA5B0;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: #15192A;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: #E4E6E9;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}
@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}
.ali::before {
    content: "";
    position: absolute;
    left: 33px;
    bottom: -30px;
    width: 76px;
    height: 78px;
    background-color: rgb(245 246 248 / var(--tw-bg-opacity, 1));
    border-radius: 50px 50px 0 0;
}



.SeoSection h2{
    padding-right: 1.5rem;
    border-right: 0.3rem solid #1F84FB;
}
.SeoSection a {
    color: #1F84FB;
}
/* .SeoSection ul li::before {
    content: "•";
    color: #3c53c7; 
    font-weight: bold;
    font-size: 2rem;
    align-items: center;
    margin-left: 0.5rem;
  } */

/* bullet for ul */
.SeoSection ul {
    list-style: none;
    padding-right: 1.5rem;
  }
  
  .SeoSection ul li {
    position: relative;
    padding-right: 1rem;
    line-height: 1.6;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  
  /* Default bullet (filled) */
  .SeoSection ul li::before {
    content: '•';
    position: absolute;
    right: -15px;
    top: 65%;
    transform: translateY(-50%);
    color: #1F84FB;
    font-size: 2.5em;
    line-height: 1;
  }
  
  /* Hollow circle for items with data-list="checked" */
  .SeoSection ul li[data-list="checked"]::before {
    content: '';
    width: 0.6em;
    height: 0.6em;
    border: 2px solid #1F84FB;
    border-radius: 50%;
    background: transparent;
    font-size: initial; /* reset if needed */
  }
  

  
  .SeoSection ol li {
    list-style: decimal;
    margin-bottom: 1rem;
  }
  .SeoSection ol {
    padding-right: 1.5rem;
  }
  .SeoSection ol li::marker {
    color: #3c53c7;
    font-weight: bold;
    font-size: 1.2rem;
  }

  .blog-pluses::before {
    content: "";
        position: absolute;
        top: 5px;
        right: -1px;
        width: 65px;
        height: 95%;
        background-image: url("/assets/images/pluses.png");
        background-repeat: repeat-y;
        background-position: right;
        /* z-index: 1; */
  }



  @media screen and (min-width: 768px) {
    .services-items li {
        width: 14%;
    }

}
@media not all and (width >=768px) {
    .blog-pluses::before {
        display: none;
      }
  
}