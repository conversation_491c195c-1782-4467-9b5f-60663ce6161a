import SpecialPrice from "@/components/common/SpecialPrice"
import Image from "next/image"
import clsx from "clsx"
import { CartApiItem } from "@/lib/context/cart-context";
import EmptyCover from "@/public/assets/images/empty-cover.webp"
interface OrderSummaryListProps {
    className?: string;
    items?: CartApiItem[];
    total?: number;
    total_discount?: number;
    user_id?: string | number;
    subtotal?: number;
}

const OrderSummaryList = ({ className, items }: OrderSummaryListProps) => {
    return (
        <div className={clsx('flex md:w-[85%]', className)}>
            <div className='flex flex-wrap justify-between max-md:gap-3 w-full'>
                {
                    items?.map(order => (
                        <div key={order.id} className="body md:w-[50%] md:flex-wrap mt-5 flex max-md:flex-col md:justify-between md:px-3 md:items-center max-md:gap-4">
                            <div className='flex items-center gap-5'>
                                <div className='w-32 h-24 relative px-3.5 max-md:w-24 border-[2px] border-dashed bg-gray-50 rounded-3xl'>
                                    <Image src={order.image || EmptyCover} fill className='rounded-lg' alt='product-img' />
                                </div>
                                <div>
                                    <span className='max-md:text-sm'> تعداد: {order.quantity} کالا </span>
                                    <h3 className='max-md:text-sm'>
                                        {order.name}
                                    </h3>
                                    <div className="flex gap-3">
                                        {order.attributes?.map((attribute, index) => (
                                            <div key={index} className="flex gap-2">
                                                <span>
                                                (
                                                    {attribute.title}:
                                                </span>
                                                <span className="text-sm font-bold text-gray-700">
                                                    {attribute.value}
                                                )
                                                </span>
                                                
                                            </div>
                                        ))}
                                    </div>
                                    {
                                        order.sale_price ?
                                            <>
                                                <SpecialPrice price={order.price} className='max-md:text-xs' />
                                                <span className='mr-3 text-red-400 font-bold text-xl max-md:text-sm'>{order?.sale_price?.toLocaleString()} تومان</span>
                                            </>
                                            :
                                            <span className='mr-3 text-red-400 font-bold text-xl max-md:text-sm'>{order?.price?.toLocaleString()} تومان</span>

                                    }
                                </div>
                            </div>

                        </div>

                    ))
                }
                {/* <div className="body mt-5 flex max-md:flex-col md:justify-between md:px-3 md:items-center max-md:gap-4">
                    <div className='flex items-center gap-5'>
                        <div className='w-32 px-3.5 max-md:w-24 border-[2px] border-dashed bg-gray-50 rounded-3xl'>
                            <Image src={ProductImage} className='' alt='product-img' />
                        </div>
                        <div>
                            <span className='max-md:text-sm'> تعداد: 1 کالا </span>
                            <h3 className='max-md:text-sm'>
                                اسم محصول در اینجا قرار میگیرد
                            </h3>
                            <SpecialPrice price='80000' className='max-md:text-xs' />
                            <span className='mr-3 text-red-400 font-bold text-xl max-md:text-sm'>50000 تومان</span>
                        </div>
                    </div>

                </div>
                <div className="body mt-5 flex max-md:flex-col md:justify-between md:px-3 md:items-center max-md:gap-4">
                    <div className='flex items-center gap-5'>
                        <div className='w-32 px-3.5 max-md:w-24 border-[2px] border-dashed bg-gray-50 rounded-3xl'>
                            <Image src={ProductImage} className='' alt='product-img' />
                        </div>
                        <div>
                            <span className='max-md:text-sm'> تعداد: 1 کالا </span>
                            <h3 className='max-md:text-sm'>
                                اسم محصول در اینجا قرار میگیرد
                            </h3>
                            <SpecialPrice price='80000' className='max-md:text-xs' />
                            <span className='mr-3 text-red-400 font-bold text-xl max-md:text-sm'>50000 تومان</span>
                        </div>
                    </div>

                </div> */}

            </div>
        </div>
    )
}

export default OrderSummaryList