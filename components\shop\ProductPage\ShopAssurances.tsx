import { Bad<PERSON><PERSON><PERSON><PERSON>, CalendarChe<PERSON>, Headset, Truck } from 'lucide-react'

const ShopAssurances = () => {
  return (
    <div className='container bg-white rounded-3xl text-sm mb-10 max-md:text-xs flex md:justify-between max-md:gap-3 px-4 max-md:py-2 max-md:px-2 min-h-28 max-md:flex-wrap'>
        <div className='flex items-center gap-2 max-md:w-full max-md:my-3 max-md:text-sm'>
            <div className=''>
                <Truck className='bg-[#FFD03E] text-white p-2 w-16 h-16 max-md:w-12 max-md:h-12 rounded-full border-[6px] border-[#FDF9D2]' />
            </div>
            <div className='flex flex-col gap-1 pr-3.5 max-md:pr-2 border-r-2 max-md:gap-2'>
                <span className='whitespace-nowrap'>
                    امکان تحویل اکسپرس
                </span>
                <span className=' '>
                    سرعت ارسال بالا با اکسپرس
                </span>
            </div>
        </div>
        <div className='flex items-center gap-2 max-md:w-full max-md:my-3 max-md:text-sm'>
            <div>
                <Headset className='bg-[#31CAFD] text-white p-2 w-16 h-16 max-md:w-12 max-md:h-12 rounded-full border-[6px] border-blue-100' />
            </div>
            <div className='flex flex-col gap-1 pr-3.5 max-md:pr-2 border-r-2 max-md:gap-2'>
                <span className=''>
                    پشتیبانی قوی
                </span>
                <span className=' max-md:whitespace-nowrap'>
                    7 روز هفته و 24 ساعته
                </span>
            </div>
        </div>
        <div className='flex items-center gap-2 max-md:w-full max-md:my-3 max-md:text-sm'>
            <div>
                <CalendarCheck className='bg-[#4BE378] text-white p-3 w-16 h-16 max-md:w-12 max-md:h-12 rounded-full border-[6px] border-green-200' />
            </div>
            <div className='flex flex-col gap-1 pr-3.5 max-md:pr-2 border-r-2 max-md:gap-2'>
                <span className=''>
                هفت روز ضمانت کالا
                </span>
                <span className=''>
                    با خیال راحت خرید کنید
                </span>
            </div>
        </div>
        <div className='flex items-center gap-2 max-md:w-full max-md:my-3 max-md:text-sm'>
            <div>
                <BadgeCheck className='bg-[#FC6A47] text-white p-2 w-16 h-16 max-md:w-12 max-md:h-12 rounded-full border-[6px] border-red-200' />
            </div>
            <div className='flex flex-col gap-1 pr-3.5 max-md:pr-2 border-r-2 max-md:gap-2'>
                <span className=''>
                ضمانت اصل بودن کالا
                </span>
                <span className=''>
                تیم‌کنترل‌کیفی‌اطمینان‌خرید </span>
            </div>
        </div>
    </div>
  )
}

export default ShopAssurances