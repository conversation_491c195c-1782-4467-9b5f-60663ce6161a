import CheckoutProgress from '@/components/shop/checkout/CheckoutProgress'
// import BasketCart from '@/components/shop/checkout/BasketCart'
// import DiscountCode from '@/components/shop/checkout/DiscountCode'
// import FactorCard from '@/components/shop/checkout/FactorCard'
// import PaymentMethodCard from '@/components/shop/checkout/PaymentMethodCard'
// import OrderSummary from '@/components/shop/checkout/OrderSummary'
// import UserAddresses from '@/components/shop/checkout/UserAddresses'
// import { getUserCart } from '@/actions/cart.action'
import CartPageClient from '@/components/shop/checkout/CartPageClient'



const CheckoutCartPage = async () => {
  
  
  return (
    <>
      <main className='container mx-auto  mb-16'>

        <CheckoutProgress
          steps={[
            { title: 'سبد خرید', status: 'current' },
            { title: 'انتخاب آدرس', status: 'upcoming' },
            { title: 'پرداخت', status: 'upcoming' },
          ]}
        />
        <CartPageClient />


      </main>
    </>

  )
}

export default CheckoutCartPage