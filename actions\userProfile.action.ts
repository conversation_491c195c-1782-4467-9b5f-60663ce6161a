"use server"
import { UserProfileData } from "@/lib/types/types";

export async function updateUserProfile(userData: FormData) {
    try {
        const response = await fetch("https://shop-khodrox.liara.run/api/v1/profile", {
            method: "POST",
            headers: {
                "Authorization": "matin_token",
                "X-Application-Token": "matin_token",
                "Accept": "application/json",
            },
            body: userData
    })
       const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}
export async function getUserProfile() {
    try {
        const response = await fetch("https://shop-khodrox.liara.run/api/v1/profile", {
            method: "GET",
            headers: {
                "Authorization": "matin_token",
                "Accept": "application/json",
                "X-Application-Token": "matin_token",
                "Content-Type": "application/json"
            },
    })
       const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}
