"use server"
import { apiClient } from "@/lib/apiClient";
import { UserProfileData } from "@/lib/types/types";

export async function updateUserProfile(userData: FormData) {
    try {
        // const response = await fetch(`${process.env.BASE_URL_2}/profile}`, {
        //     method: "POST",
        //     headers: {
        //         "Authorization": ,
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json",
        //     },
        //     body: userData
        // })
        const response = await apiClient("profile", {
            base: "alt",
            method: "POST",
            body: userData,
        
            
        },true)
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}
export async function getUserProfile() {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/profile", {
        //     method: "GET",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Accept": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     },
        // })
        const response = await apiClient("profile", {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("guiohuiouio8", error);
        return { success: false, error: "Failed to delete address" };
    }
}
