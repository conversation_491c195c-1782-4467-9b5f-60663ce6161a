import Link from "next/link"
import {ChevronRight} from "lucide-react"
import {Category} from "@/lib/types/category.types";

interface BreadcrumbProps {
    categories: Category[]
    className?: string
}

export default function Breadcrumb({categories, className = ""}: BreadcrumbProps) {
    return (
        <nav
            className={`flex items-center space-x-1 max-w-7xl mx-auto max-md:bg-white pt-5 md:pt-10 pb-2 px-5  rtl:space-x-reverse text-sm text-muted-foreground ${className}`}
            dir="rtl"
            aria-label="Breadcrumb"
        >
            {categories.map((category, index) => (
                <div key={category.slug} className="flex items-center">
                    {index === categories.length - 1 ? (
                        // Last item - not clickable
                        <span className="text-foreground text-xs md:text-sm font-medium">{category.title}</span>
                    ) : (
                        // Clickable items
                        <Link href={`/category/${category.slug}`}
                              className="hover:text-foreground whitespace-nowrap text-xs md:text-sm transition-colors">
                            {category.title}
                        </Link>
                    )}

                    {/* Separator - don't show after last item */}
                    {index < categories.length - 1 && <ChevronRight className="h-4 w-4 mx-1 rtl:rotate-180"/>}
                </div>
            ))}
        </nav>
    )
}