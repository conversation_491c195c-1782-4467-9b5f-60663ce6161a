'use client';

import { useState } from "react";
import { CircleCheckBig, Eye, ListCheck, Search, XCircle, Loader2 } from "lucide-react";
import Image from "next/image";

import FilterIcon from "@/components/common/svg/FilterIcon";
import PackageBoxIcon from "@/components/common/svg/PackageBoxIcon";
import UndoCircleIcon from "@/components/common/svg/UndoCircleIcon";

import { DeliveryStatusCounts, Invoice } from "@/lib/types/invoice.types";
import { getAllInvoices } from "@/actions/invoices.action";
import Link from "next/link";

const statusMap = {
    sent: {
        label: "ارسال شده",
        icon: <CircleCheckBig className="text-white" />,
        color: "text-[#6FEC94]",
        bg: "bg-[#6FEC94] border-green-100",
    },
    in_progress: {
        label: "در حال پردازش",
        icon: <UndoCircleIcon className="text-white" />,
        color: "text-primary",
        bg: "bg-gradient-to-l from-[#7BB8FF] to-[#32CCFE] border-blue-100",
    },
    done: {
        label: "تکمیل شده",
        icon: <ListCheck className="gray-red-600" />,
        color: "text-gray-500",
        bg: "bg-gray-300 border-gray-200",
    },
    reject: {
        label: "لغو شده",
        icon: <XCircle className="text-red-500" />,
        color: "text-red-500",
        bg: "bg-red-100 border-red-200",
    },
} as const;

interface OrderHistoryWrapperProps {
    invoices: Invoice[];
    delivery_status_counts: DeliveryStatusCounts;
}

const OrderHistoryWrapper = ({ invoices, delivery_status_counts }: OrderHistoryWrapperProps) => {
    const [activeFilter, setActiveFilter] = useState<'' | 'in_progress' | 'sent' | 'done' | 'canceled'>('');
    const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>(invoices);
    const [loading, setLoading] = useState(false);

    const { canceled, done, in_progress, sent } = delivery_status_counts;
    const allStatusCount = sent + in_progress + done + canceled;
    type CountKey = keyof typeof counts;

    const filters: { label: string; value: '' | 'in_progress' | 'sent' | 'done' | 'canceled'; countKey: CountKey }[] = [
        { label: 'همه', value: '', countKey: 'all' },
        { label: 'در حال پردازش', value: 'in_progress', countKey: 'in_progress' },
        { label: 'ارسال شده', value: 'sent', countKey: 'sent' },
        { label: 'تکمیل شده', value: 'done', countKey: 'done' },
        { label: 'لغو شده', value: 'canceled', countKey: 'canceled' },
    ];

    const counts = {
        all: allStatusCount,
        in_progress,
        sent,
        done,
        canceled,
    };
    const handleFilterClick = async (status: typeof activeFilter) => {
        setActiveFilter(status);
        setLoading(true);
        const query = status ? { delivery_status: status } : {};
        const res = await getAllInvoices(query);
        if (res.success) {
            setFilteredInvoices(res.data.invoices);
        }
        setLoading(false);
    };

    return (
        <>
            <div className="text-black flex items-center gap-3 p-3 h-20 px-5">
                <div className="p-4 px-4 h-full bg-gradient-to-t from-gray-100 to-transparent rounded-b-full">
                    <PackageBoxIcon size={25} />
                </div>
                <h1 className="md:text-xl">تاریخچه سفارشات</h1>
            </div>

            <div className="flex justify-between items-center p-3 px-5 w-full">
                <div className="flex flex-col md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10 max-md:px-3 ">
                    <div className="flex items-center gap-2 max-md:hidden">
                        <FilterIcon />
                        <span>مرتب سازی:</span>
                    </div>

                    <div className="hidden md:flex md:gap-8 h-full">
                        {filters.map(({ label, value, countKey }) => {
                            const isActive = activeFilter === value;
                            return (
                                <button
                                    key={value}
                                    onClick={() => handleFilterClick(value)}
                                    className={`transition-all ${isActive ? 'text-primary border-b-2 border-primary' : ''}`}
                                >
                                    {label}
                                    <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-[#F7BC06] text-black' : 'bg-[#F9FAFB] text-gray-500'}`}>
                                        {counts[countKey]}
                                    </span>
                                </button>
                            );
                        })}
                    </div>

                    {/* mobile version */}
                    <div className="w-full md:hidden">
                        <div className="relative w-full max-w-xs">
                            
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                                <FilterIcon className="w-5 h-5 text-gray-400" />
                            </div>

                            <select
                                value={activeFilter}
                                onChange={(e) => handleFilterClick(e.target.value as typeof activeFilter)}
                                className="w-full appearance-none border border-gray-300 rounded-xl py-2 pr-10 pl-4 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
                            >
                                {filters.map(({ label, value }) => (
                                    <option key={value} value={value}>
                                        {label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>


                <div className="flex md:gap-8 items-center">
                    <div className="relative max-md:hidden">
                        <input
                            type="text"
                            placeholder="جستجو"
                            className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500 border rounded-3xl outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                        />
                        <div className="absolute left-5 top-1/2 transform -translate-y-1/2 cursor-pointer">
                            <Search className="w-5 h-5 text-gray-400" />
                        </div>
                    </div>
                    <p>{filteredInvoices.length} سفارش</p>
                </div>
            </div>

            <div className="mt-3 p-3 px-5 min-h-[400px] flex flex-col">
                {loading ? (
                    <div className="flex flex-1 justify-center items-center min-h-[300px]">
                        <Loader2 className="animate-spin w-10 h-10 text-primary" />
                    </div>
                ) : filteredInvoices.length === 0 ? (
                    <div className="flex flex-1 justify-center items-center min-h-[300px] text-gray-400 text-lg">
                        هیچ سفارشی برای نمایش وجود ندارد
                    </div>
                ) : (
                    filteredInvoices.map((invoice) => {
                        const status = invoice.delivery_status as keyof typeof statusMap;
                        const current = statusMap[status] || statusMap.done;

                        return (
                            <Link href={`/dashboard/order-details/${invoice.id}`} key={invoice.id} className="bg-[#FAFAFA] p-3 rounded-2xl mb-4">
                                <div className="flex justify-between items-center">
                                    <div className="flex gap-2 items-center">
                                        <div className={`w-9 h-9 ${current.bg} flex justify-center items-center rounded-full border-4`}>
                                            {current.icon}
                                        </div>
                                        <span className="!text-base !font-thin"> وضعیت: {current.label} </span>
                                    </div>
                                    <button className="text-gray-400 hover:text-gray-600">
                                        <Eye className="w-5 h-5" />
                                    </button>
                                </div>

                                <div className="flex items-center md:gap-10 gap-3 mt-4 px-2 text-black max-md:flex-wrap">
                                    <p className="text-sm max-md:text-xs max-md:w-[45%]"><span className="text-gray-600 ml-1">کد پیگیری:</span> #{invoice.invoice_number}</p>
                                    <p className="text-sm max-md:text-xs max-md:w-[45%]"><span className="text-gray-600 ml-1">تاریخ:</span> {invoice.creation_date}</p>
                                    <p className="text-sm max-md:text-xs max-md:w-[45%]"><span className="text-gray-600 ml-1">تعداد کالا:</span> - </p>
                                    <p className="text-sm max-md:text-xs max-md:w-[45%]"><span className="text-gray-600 ml-1">مبلغ:</span> {invoice.total.toLocaleString()} تومان</p>
                                </div>

                                <div className="flex gap-5 max-md:gap-2 mt-8 px-2">
                                    {invoice.products.map((product) => (
                                        <div key={product.id} className="bg-white rounded-xl">
                                            <Image
                                                src={product.image || ""}
                                                alt="product"
                                                className="object-cover h-[120px] max-md:w-[70px] max-md:h-[60px] rounded-xl"
                                                width={150}
                                                height={120}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </Link>
                        );
                    })
                )}
            </div>
        </>
    );
};

export default OrderHistoryWrapper;
