"use client"

import {useSearch} from "@/lib/hooks/useSearch";
import {SearchDesktopInput} from "@/components/common/SearchDesktopInput";
import SearchResults from "@/components/common/SearchResults";

export default function SearchDesktopWrapper() {
    const {productParams, setProductParams, paginatedQuery} = useSearch();

    return (<>
        <SearchDesktopInput
            setProductParams={setProductParams}
            value={productParams.search || ""}
        >
            <SearchResults
                isLoading={paginatedQuery.isLoading}
                products={paginatedQuery.data?.data?.products || []}
                searchValue={productParams.search}
            />
        </SearchDesktopInput>
    </>)
}
