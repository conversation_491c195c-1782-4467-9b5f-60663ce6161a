import ChatSmileIcon from "@/components/common/svg/ChatSmileIcon"
import FilterIcon from "@/components/common/svg/FilterIcon"
import { ChevronsLeft, Plus, Search } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import ChatSmile from "@/public/assets/images/chat-smile.png"
import ChatIcon from "@/components/common/svg/ChatIcon"
import ChatIconFilled from "@/components/common/svg/ChatIconFilled"
import MoreVerticalDots from "@/components/common/svg/MoreVerticalDots"
const TicketsPage = () => {
    return (
        <section>
            <div className="w-full rounded-2xl bg-white flex justify-between items-center px-4 h-20">
                <div className="flex items-center gap-3">
                    <FilterIcon />
                    <Link href="#" >تیکت های پشتیبانی</Link>
                    <Link href="#" >تیکت های خریدار</Link>
                </div>
                <div className="flex items-center gap-3">
                    <p>
                        ایجاد تیکت
                    </p>
                    <button className="p-2.5  bg-yellow text-white rounded-full">
                        <Plus />
                    </button>
                </div>
            </div>
            <div className="mt-8 flex justify-between">
                <div className="bg-white rounded-2xl p-4 w-[40%]">
                    <div className="flex justify-between px-5">
                        <div className="flex items-center gap-2">
                            <ChatSmileIcon />
                            <p>تیکت ها</p>
                            <span> (27) </span>
                        </div>
                        <div>
                            <Image src={ChatSmile} alt="chat-smile" />
                        </div>
                    </div>
                    <div>
                        <div className="w-full relative px-5 mt-5">
                            <input
                                // ref={inputRef}
                                type="text"
                                // value={searchValue}
                                // onChange={handleChange}
                                // onKeyDown={handleKeyDown}
                                // onFocus={handleFocus}
                                placeholder="جستجو"
                                className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500  border rounded-xl outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                            />
                            <div
                                // onClick={handleLinkClick}
                                className="absolute left-10 top-1/2 transform -translate-y-1/2 cursor-pointer">
                                <Search className="w-5 h-5 text-gray-400" />
                            </div>

                            {/* {searchValue && (
                                <button
                                    className="absolute right-4 top-1/2 transform -translate-y-1/2"
                                >
                                    <X
                                        onClick={handleClear}
                                        className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                                </button>
                            )} */}
                        </div>
                    </div>
                    <div className="mt-8 p-3 pt-5 border-t">
                        <div className="px-4 flex justify-between items-end">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base ">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-[#9DA5B0] px-2.5 py-[2px] rounded-full">بسته شده</span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                    </div>
                    <div className="mt-3 p-3 pt-5 bg-[#F4F6F8] rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-[#1f39fb] rounded-full">
                                    <ChatIconFilled />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base text-primary">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-yellow text-sm px-2.5 py-[2px] rounded-full"> منتظر پاسخ </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-primary text-sm px-2.5 py-[2px] rounded-full"> در حال بررسی </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-[#2DC058] text-sm px-2.5 py-[2px] rounded-full"> پاسخ داده شده </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-[#2DC058] text-sm px-2.5 py-[2px] rounded-full"> پاسخ داده شده </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                    </div>
                </div>
                <div className="bg-white rounded-2xl p-4 w-[58%]">
                    <div className="flex justify-between items-center">
                        <div className="flex gap-3 items-center">
                            <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                <ChatIcon />
                            </div>
                            <div className="flex flex-col justify-between gap-2">
                                <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                <div className="flex gap-3 text-sm items-center">
                                    <p>
                                        2 دقیقه پیش
                                    </p>
                                    <p>
                                        شماره پیام: #13789
                                    </p>
                                    <p>
                                        پاسخ دهنده: مائده صادقی
                                    </p>
                                </div>
                            </div>

                        </div>
                        <div className="flex flex-col gap-3 items-end justify-end mt-auto">
                            <MoreVerticalDots className="ml-2" width={18}  />
                            <span className="text-white bg-[#2DC058] text-sm px-2.5 py-[2px] rounded-full"> پاسخ داده شده </span>
                        </div>
                    </div>

                </div>
            </div>
        </section>
    )
}

export default TicketsPage