import React, {useCallback, useMemo} from 'react';
import AccordionHeader from './AccordionHeader';
import {CategorySearchableAttributes} from "@/lib/types/category.types";

type Props = {
    sizes?: CategorySearchableAttributes;
    selectedSizes?: string; // comma-separated string
    onChange?: (value: string) => void;
};

const SelectSize = ({sizes, selectedSizes, onChange}: Props) => {
    const selectedArray = useMemo(() => {
        return selectedSizes?.split(',').filter(Boolean) || [];
    }, [selectedSizes]);

    const handleToggle = useCallback((size: string) => {
        const updated = selectedArray.includes(size)
            ? selectedArray.filter(s => s !== size)
            : [...selectedArray, size];

        onChange?.(updated.join(','));
    }, [selectedArray, onChange]);

    console.log('sizes is >>>', sizes);

    if (!sizes || (sizes && sizes.values.length === 0)) return null;

    return (
        <div
            className='bg-white max-md:border  max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter'>
            <AccordionHeader title='انتخاب سایز'>
                <div className='mt-5'>
                    <ul className='flex flex-col gap-5 text-sm font-light'>
                        {sizes?.values?.map((size, index) => (
                            <li key={index} className="flex justify-between items-center mb-2">
                                <div className='flex items-center'>
                                    <div className="custom-checkbox px-0">
                                        <label className="flex items-center gap-2 text-sm font-medium">
                                            <input
                                                type="checkbox"
                                                checked={selectedArray.includes(size.title)}
                                                onChange={() => handleToggle(size.title)}
                                            />
                                            {size.title}
                                        </label>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            </AccordionHeader>
        </div>
    );
};

export default SelectSize;