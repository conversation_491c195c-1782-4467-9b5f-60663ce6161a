"use client"
import { PageDescription } from './PageDescription'
import Card from './Card'
import CustomButton from '../UI/CustomButton'
import InquiryResult from '../inquiry/result/InquiryResult'
import { getVehicleInquiry } from '@/actions/inquiry.action'
import { useEffect, useState } from 'react'
// import CallCookie from './CallCookie'
import { CarPaymentDetails } from '@/lib/types/action-types'
import { useRouter } from 'next/navigation'

interface ActionResult<T = unknown> {
  success: boolean;
  message?: string;
  data?: T
}

function MessageCard({ message }: { message: string }) {
  const router = useRouter()
  console.log(router);
  useEffect(() => {
    router.refresh()
  }, [])

  return (
    <Card className='mt-18 md:mt-20'>
      <div className='flex justify-center items-center flex-col gap-y-7'>
        <p className='text-sm md:text-base font-semibold'>{message}</p>
        <CustomButton href='/' className='py-4'>بازگشت به صفحه اصلی</CustomButton>
      </div>
    </Card>
  )
}

interface InquiryResultWrapperProps {
  isReInquiry: boolean;
  traceNumber: string;
  token?: string
}

const InquiryResultWrapper = ({ isReInquiry, traceNumber }: InquiryResultWrapperProps) => {
  const [isMotor, setIsMotor] = useState(false)
  const [actionResult, setActionResult] = useState<ActionResult<CarPaymentDetails> | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function getResult() {
      try {
        setIsLoading(true)
        const response = await getVehicleInquiry(traceNumber)
        setActionResult(response)
        console.warn(response);


        if (response?.data?.type === 'motor') {
          setIsMotor(true)
        } else {
          setIsMotor(false)
        }
      } catch (error) {
        console.error("Failed to fetch inquiry:", error)
        setActionResult({
          success: false,
          message: "ارتباط با سرور برقرار نشد. لطفاً مجدداً تلاش کنید."
        })
      } finally {
        setIsLoading(false)
      }
    }

    getResult()
  }, [traceNumber])

  if (isLoading) {
    return (
      <div className='w-full max-w-[470px] mb-20 md:mb-32 relative z-20'>
        <PageDescription
          title="در حال دریافت نتیجه استعلام"
          description='لطفاً صبر کنید...'
        />
        <Card className='mt-18 md:mt-20'>
          <div className='flex justify-center items-center py-10'>
            <p className='text-sm md:text-base font-semibold'>در حال بارگذاری...</p>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className='w-full max-w-[470px] mb-20 md:mb-32 relative z-20'>

      <PageDescription
        title={isMotor ? "استعلام و پرداخت خلافی موتور سیکلت" : "استعلام و پرداخت خلافی خودرو"}
        description='استعلام شما انجام شد و نتیجه در زیر برای شما نمایش داده میشود'
      />
      <div className='mt-5 md:mt-10 overflow-visible pb-20'>
        {actionResult?.success ? (
          <>
            {(actionResult?.data && actionResult.data.plaque) ? (
              <InquiryResult
                isReInquiry={isReInquiry}
                inquiryDetail={actionResult.data}
              />
            ) : (
              <MessageCard message={actionResult?.data?.message ?? "با عرض پوزش. مشکلی از سمت سرور پیش آمد."} />
            )}
          </>
        ) : (
          <MessageCard message={actionResult?.message ?? "با عرض پوزش. مشکلی از سمت سرور پیش آمد."}/>
        )}
      </div>
    </div>
  )
}

export default InquiryResultWrapper