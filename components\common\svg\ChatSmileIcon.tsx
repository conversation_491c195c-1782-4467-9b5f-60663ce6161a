import React from "react"

interface ChatSmileIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const ChatSmileIcon: React.FC<ChatSmileIconProps> = ({
  size = 27,
  color = "#9da5b0",
  ...props
}) => (
  <svg
    width={size}
    height={(size * 25) / 27} // maintain original aspect ratio
    viewBox="0 0 27 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.23 14.487a.929.929 0 0 0-1.319-.192.975.975 0 0 0-.188 1.346Zm10.047 1.154a.975.975 0 0 0-.188-1.346.929.929 0 0 0-1.319.192ZM12.244 1.923h2.512V0H12.244ZM1.884 18.91V12.5H0v6.41Zm12.872 4.167H5.965V25h8.791ZM0 18.91A6.028 6.028 0 0 0 5.965 25V23.077A4.125 4.125 0 0 1 1.884 18.91ZM25.116 12.5a10.47 10.47 0 0 1-10.36 10.577V25A12.374 12.374 0 0 0 27 12.5ZM14.756 1.923A10.47 10.47 0 0 1 25.116 12.5H27A12.374 12.374 0 0 0 14.756 0ZM12.244 0A12.374 12.374 0 0 0 0 12.5H1.884A10.47 10.47 0 0 1 12.244 1.923ZM8.477 15.064l-.753.578h0v0l0 0 .008.011.024.032c.02.026.047.06.082.1.07.085.169.2.3.338a8.464 8.464 0 0 0 1.117.994A7.113 7.113 0 0 0 13.5 18.59V16.667a5.256 5.256 0 0 1-3.135-1.1A6.572 6.572 0 0 1 9.5 14.8c-.1-.1-.169-.188-.215-.243l-.048-.06-.009-.011h0l-.753.577ZM13.5 18.59a7.113 7.113 0 0 0 4.243-1.466 8.468 8.468 0 0 0 1.117-.994c.128-.137.228-.253.3-.338.035-.042.062-.077.082-.1l.024-.032.008-.011 0 0v0h0s0 0-.753-.578l-.753-.577h0l-.009.011-.048.06c-.046.056-.118.14-.215.243a6.573 6.573 0 0 1-.865.769 5.256 5.256 0 0 1-3.135 1.1Z"
      fill={color}
    />
  </svg>
)

export default ChatSmileIcon
