
import { AllInvoicesResponse } from "@/lib/types/invoice.types"
import OrderHistoryWrapper from "@/components/Dashboard/orderHistory/OrderHistoryWrapper"
import { apiClient } from "@/lib/apiClient"


const OrderHistoryPage = async () => {

  // const response = await fetch(`https://shop-khodrox.liara.run/api/v1/invoices`, {
  //   method: "GET",
  //   headers: {
  //     "Authorization": "matin_token",
  //     "Content-Type": "application/json",
  //     "X-Application-Token": "matin_token",
  //     "Accept": "application/json"
  //   }
  // })
  const response = await apiClient("invoices", {
    base: "alt",
    method: "GET",
  })
  const invoiceResponse: AllInvoicesResponse = await response.json()
  const invoices = invoiceResponse?.data?.invoices ?? []
  console.log(invoiceResponse);
  const delivery_status_counts = invoiceResponse.data.delivery_status_counts
  
  return (
    <section className="bg-white rounded-xl">
      <OrderHistoryWrapper invoices={invoices}  delivery_status_counts={delivery_status_counts} />
    </section>
  )
}

export default OrderHistoryPage