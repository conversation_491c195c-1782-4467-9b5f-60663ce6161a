"use client";

import React from 'react';
import Image from 'next/image';
import { Plus, Minus, ShoppingCart, X } from 'lucide-react';
import { CartApiItem, useCart } from '@/lib/context/cart-context';
import SpecialPrice from '@/components/common/SpecialPrice';
import CustomButton from '@/components/UI/CustomButton';
import ProductImage from '@/public/assets/images/product1.png';
import { useRouter } from 'next/navigation';
interface CartPopupProps {
  cartItems: CartApiItem[];
  onClose: () => void;
}


const CartPopup: React.FC<CartPopupProps> = ({ cartItems, onClose }) => {
  const { checkoutAddToCart, checkoutDecreaseFromCart, finalPrice, totalItems, isUpdating } = useCart();
  // const [updatingButton, setUpdatingButton] = useState<'add' | 'decrease' | null>(null);
  const router = useRouter()
  const handleAddToCart = (productId: string) => {
    checkoutAddToCart({ id: productId, quantity: 1 });
  };

  const handleDecreaseFromCart = (productId: string) => {
    checkoutDecreaseFromCart(productId);
  };
  const handleGoToCheckoutPage = () => {
    onClose();
    router.push('/checkout/cart');
  }

  // Show empty cart message if no items
  if (cartItems.length === 0) {
    return (
      <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-3xl shadow-lg border border-gray-100 p-6 z-50">
        <div className="flex flex-row-reverse items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-gray-800">سبد خرید</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        <div className="text-center py-8">
          <ShoppingCart className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500 text-sm">سبد خرید شما خالی است</p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-3xl shadow-lg border border-gray-100 z-50 md:max-h-[30rem] max-h-96 flex flex-col">
      {/* Header - Fixed */}
      <div className="flex flex-row-reverse items-center justify-between p-6 pb-4 flex-shrink-0">
        <div>
          <h3 className="text-lg font-bold text-gray-800">سبد خرید</h3>
          <p className="text-sm text-gray-500">{totalItems} محصول</p>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>

      {/* Cart Items - Scrollable */}
      <div className="flex-1 overflow-y-auto px-6">
        <div className="space-y-4 pb-4">
          {cartItems.map((item) => (
            <div key={item.id} className="flex flex-col gap-3 p-3 bg-gray-50 rounded-2xl">
              <div className='flex items-center gap-3 bg-gray-50 rounded-2xl'>
                {/* Product Image */}
                <div className="relative w-16 h-16 bg-white rounded-xl border border-gray-200 flex-shrink-0">
                  <Image
                    src={item.image || ProductImage}
                    alt={item.name || 'محصول'}
                    fill
                    className="object-cover rounded-xl"
                  />
                </div>

                {/* Product Info */}
                <div className="flex flex-col min-w-0 items-start">
                  <h4 className="text-sm font-medium text-gray-800 truncate p-0">
                    {item.name || 'محصول'}
                  </h4>
                  <div className="flex gap-3 text-sm">
                    {item.attributes?.map((attribute, index) => (
                      <div key={index} className="inline">
                        <span className='ml-2'>
                          (
                          {attribute.title}:
                        </span>
                        <span className="text-sm font-bold text-gray-700">
                          {attribute.value}
                          )
                        </span>

                      </div>
                    ))}
                  </div>

                </div>

              </div>
                {/* Price */}
                <div className="flex justify-between items-center gap-2 mt-1">
                  {item.sale_price ? (
                    <>
                      <span className="text-sm font-bold text-red-500">
                        {item.sale_price.toLocaleString()} تومان
                      </span>
                      <SpecialPrice price={item.price} className="text-xs" />
                    </>
                  ) : (
                    <span className="text-sm font-medium text-gray-700">
                      {item.price.toLocaleString()} تومان
                    </span>
                  )}
                  {/* Quantity Controls */}
                  <div className={`flex flex-row-reverse items-center gap-2 bg-white rounded-full p-1 border border-gray-200 ${isUpdating ? 'opacity-70' : ''}`}>
                    <button
                      onClick={() => handleDecreaseFromCart(item.id || '')}
                      className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                      disabled={isUpdating === "add" || isUpdating === "decrease"}
                    >
                      {isUpdating === "decrease" ? (
                        <div className="w-2 h-2 border border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Minus className="w-3 h-3 text-gray-600" />
                      )}
                    </button>

                    <span className="text-sm font-medium min-w-[20px] text-center">
                      {item.quantity}
                    </span>

                    <button
                      onClick={() => handleAddToCart(item.id || '')}
                      className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                      disabled={isUpdating === "add" || isUpdating === "decrease"}
                    >
                      {isUpdating === "add" ? (
                        <div className="w-2 h-2 border border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Plus className="w-3 h-3 text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>

            </div>
          ))}
        </div>
      </div>

      {/* Total and Actions - Fixed at bottom */}
      <div className="border-t border-gray-200 p-6 pt-4 flex-shrink-0">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-gray-600">مجموع:</span>
          <span className="text-lg font-bold text-gray-800">
            {finalPrice.toLocaleString()} تومان
          </span>
        </div>

        <CustomButton
          className="w-full py-3 text-sm font-medium"
          onClick={handleGoToCheckoutPage}          
        >
          <ShoppingCart className="w-4 h-4" />
          مشاهده سبد خرید
        </CustomButton>
      </div>
    </div>
  );
};

export default CartPopup;
