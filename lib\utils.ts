import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import faqs from "@/data/faqs.json"
import services_status from "@/data/services_status.json"
import { apiClient } from './apiClient';
import jalaali from 'jalaali-js'
import { PageContentResponse } from "./types/types";

export function getCurrentJalaliDate(): string {
  const now = new Date()
  const { jy, jm, jd } = jalaali.toJalaali(now)

  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${jy}/${pad(jm)}/${pad(jd)}`
}



export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getFaqs(category: string) {
  const faqCategory = category

    return faqs.faqs.find((faq) => faq.category === faqCategory)?.questions || [];
}
export async function getServicesStatus() {
    
    return services_status.services
}


export async function getPageContent(pageAddress: string): Promise<PageContentResponse> {
  const res = await apiClient(`article/${pageAddress}`);
  const json = await res.json();
  return json.data;
}

export async function getUserAddresses() {
    try {
        const response = await fetch("https://shop-khodrox.liara.run/api/v1/addresses", {
            method: "GET",
            headers: {
                "Authorization": "matin_token",
                "Content-Type": "application/json",
                "X-Application-Token": "matin_token",
                "Accept": "application/json"
            }
        });
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to get addresses" };
    }
}