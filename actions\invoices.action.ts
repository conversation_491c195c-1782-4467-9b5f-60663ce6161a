"use server"

type GetAllInvoicesOptions = {
  delivery_status?: string;
  limit?: number;
  page?: number;
};

export async function getAllInvoices(options: GetAllInvoicesOptions = {}) {
  const { delivery_status, limit, page } = options;

  const queryParams = new URLSearchParams();

  if (delivery_status) queryParams.append("delivery_status", delivery_status);
  if (limit !== undefined) queryParams.append("limit", limit.toString());
  if (page !== undefined) queryParams.append("page", page.toString());

  const queryString = queryParams.toString();
  const url = `https://shop-khodrox.liara.run/api/v1/invoices${queryString ? "?" + queryString : ""}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Authorization": "matin_token",
        "X-Application-Token": "matin_token",
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error getting invoices:", error);
    return { success: false, error: "Failed to get invoices" };
  }
}
