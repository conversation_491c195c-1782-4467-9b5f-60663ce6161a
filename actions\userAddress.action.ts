"use server"
import { AddressFormData } from "@/components/shop/checkout/AddressModal";
import { apiClient } from "@/lib/apiClient";
// import { getUserAddresses } from "@/lib/utils";

export async function getUserAddressesAction() {
    try {
        // const response = 
        const response = await apiClient("addresses", {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to get addresses" };
    }
}

export async function createUserAddressAction(address: AddressFormData) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/addresses", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     },
        //     body: JSON.stringify(address)
        // });
        const response = await apiClient("addresses", {
            base: "alt",
            method: "POST",
            body: address,
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to get addresses" };
    }
}
export async function updateUserAddressAction(address: AddressFormData) {
    try {
        // const response = await fetch(`https://shop-khodrox.liara.run/api/v1/addresses/${address.id}`, {
        //     method: "PUT",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Content-Type": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json"
        //     },
        //     body: JSON.stringify(address)
        // });
        const response = await apiClient(`addresses/${address.id}`, {
            base: "alt",
            method: "PUT",
            body: address,
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to update address" };
    }
}

export async function deleteUserAddressAction(id: string) {
    try {
        // const response = await fetch(`https://shop-khodrox.liara.run/api/v1/addresses/${id}`, {
        //     method: "DELETE",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Content-Type": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json"
        //     }
        // });
        const response = await apiClient(`addresses/${id}`, {
            base: "alt",
            method: "DELETE",
        })
        console.log(response);

        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}