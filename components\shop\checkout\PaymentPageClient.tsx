'use client';

import { useState } from 'react';
import PaymentMethodCard from './PaymentMethodCard';
import FactorCard from './FactorCard';
import OrderSummary from './OrderSummary';
import { CartApiItem, useCart } from '@/lib/context/cart-context';
import { createInvoice } from '@/actions/payment.action';
import { CreateInvoiceResponse } from '@/lib/types/invoice.types';
import toast from 'react-hot-toast';
import { invoicePayment } from '../../../actions/payment.action';
import { useRouter } from 'nextjs-toploader/app';
import DiscountCode from './DiscountCode';

type PaymentPageClientProps = {
  orderSummaryData?: {
    items?: CartApiItem[];
    total?: number;
    total_discount?: number;
    user_id?: string;
    subtotal?: number;
  };
  addressId?: string;
};

const PaymentPageClient = ({ orderSummaryData, addressId }: PaymentPageClientProps) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('wallet');
  const [discountCode, setDiscountCode] = useState<string>("")
  const [discountCodeInput, setDiscountCodeInput] = useState("");
  const [isNavigating, setIsNavigating] = useState(false);
  const router = useRouter()
  const { cartItems } = useCart()
  const applyDiscount = (code: string) => {
    setDiscountCode(code);         
    toast.success("کد تخفیف اعمال شد"); 
  };

  const handleCreateInvoice = async () => {
    // setIsNavigating(true)
    if (cartItems.length == 0) {
      toast.error("سبد خرید خالی است. لطفا برای ادامه خرید محصولی به سبد خرید اضافه کنید", {
        duration: 10000
      })
      return
    }
    debugger
    if (addressId) {
      const response: CreateInvoiceResponse = await createInvoice(addressId, discountCode ?? null);
      console.log(response);
      if (response.success) {
        if ('id' in response.data) {
          const payResponse = await invoicePayment(response.data.id, selectedPaymentMethod);
          console.log(payResponse);
          if (payResponse.success) {
            router.push(payResponse.data?.payment_link)            
          } else {
            // setIsNavigating(false)
            toast.error(payResponse.message)
          }
        } else {
          toast.error('Invoice ID not found in response.');
          // setIsNavigating(false)
        }
      } else {
        toast.error(response.message);
        // setIsNavigating(false)
      }
    }
    setIsNavigating(false)
  }

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
  };

  return (
    <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>
      <div className='md:w-[70%]'>
        <PaymentMethodCard
          onPaymentMethodChange={handlePaymentMethodChange}
        />
        <DiscountCode
          discountCodeInput={discountCodeInput}
          setDiscountCodeInput={setDiscountCodeInput}
          applyDiscount={applyDiscount} />

        {orderSummaryData && (
          <OrderSummary {...orderSummaryData} />
        )}
      </div>
      <FactorCard
        steps={{ title: "payment", nextStepBtnTitle: "پرداخت", nextStepBtnLink: "/checkout/result" }}
        paymentMethod={selectedPaymentMethod}
        onCreateInvoice={handleCreateInvoice}
        isNavigating={isNavigating}
        setIsNavigating={setIsNavigating}
      />
    </div>
  );
};

export default PaymentPageClient;
