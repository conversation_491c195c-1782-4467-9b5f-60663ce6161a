import type {Metadata} from "next";
import "../styles/globals.css";
import {Toaster} from "react-hot-toast";
import {UserProvider} from "@/lib/providers/UserProvider";
import {ServicesProvider} from "@/lib/providers/ServicesProvider";
import NextTopLoader from 'nextjs-toploader';
import localFont from 'next/font/local';
import Script from "next/script";
import ReactQueryProvider from "@/lib/providers/ReactQueryProvider";

const myCustomFont = localFont({
    src: [
        {
            path: '../public/assets/fonts/iransans/IRANSansWeb_Medium.woff2', // relative to this file
            weight: '500',
            style: 'normal',
        },
        {
            path: '../public/assets/fonts/iransans/IRANSansWeb_Bold.woff2',
            weight: '700',
            style: 'normal',

        },
    ],
    display: 'swap',
});
export const metadata: Metadata = {
    title: "khodrox",
    description: "خدمات آنلاین خودرو با خودراکس",
    icons: "/favicon.png",
};

export default function RootLayout({
                                       children,
                                   }: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="fa" className={myCustomFont.className}>
        <head>
            <Script id="gtm-head" strategy="afterInteractive">
                {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-TVKNBV6Q');`}
            </Script>
        </head>
        <body
            dir="rtl"
        >
        <ReactQueryProvider>
            <NextTopLoader/>
            <UserProvider>
                <ServicesProvider>
                    {children}
                </ServicesProvider>
            </UserProvider>
            <Toaster
                position='bottom-left'/>
        </ReactQueryProvider>
        <noscript>
            <iframe
                src="https://www.googletagmanager.com/ns.html?id=GTM-TVKNBV6Q"
                height="0"
                width="0"
                style={{display: 'none', visibility: 'hidden'}}
            ></iframe>
        </noscript>
        </body>
        </html>
    );
}
