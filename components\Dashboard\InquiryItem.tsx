import { EyeIcon } from "lucide-react"
import PlateCar from "@/public/assets/images/plate-left.png"
import CarForbidden from "@/public/assets/images/car-forbidden.png"
import MotorForbidden from "@/public/assets/images/motor-forbidden.png"
import Image from "next/image"
import { HistoryTransaction } from "@/lib/types/action-types"
import Link from "next/link"
import { TICKET_RESULT_PATH } from "@/lib/routes"
type InquiryItemTypes = {
    inquiry:HistoryTransaction
    index: number
}
const InquiryItem = ({inquiry, index}:  InquiryItemTypes) => {
    const { alphabet,left, right, mid } = inquiry.plaque
  return (
    <Link href={`${TICKET_RESULT_PATH}?traceNumber=${inquiry.trace_number}&reInquiry=true&isMotor=${inquiry.type == "motor" ? "true" : "false"}`} className={`flex justify-between items-center rounded-3xl my-2 max-md:my-8 md:py-4 ${index % 2 !== 0 ? "bg-gray-50" : ""}`}>
        <div className="flex max-md:flex-row-reverse md:gap-3 gap-1 w-full">
            <div className="max-md:order-2 gap-3 w-10 max-md:flex max-md:flex-col justify-center items-center ">
            <Image className="md:hidden shrink-0 w-5 h-5" src={inquiry.type == "car" ? CarForbidden : MotorForbidden} alt="car-forbidden" />
                <Image src={PlateCar} alt="plate-car" />
            </div>
            <div className="flex flex-col justify-between w-full md:w-9/12 max-md:pr-2 text-sm">
                <h3 className="flex md:gap-2 max-md:pt-2 w-full max-md:order-1"> <Image className="shrink-0 max-md:hidden" src={inquiry.type == "car" ? CarForbidden : MotorForbidden} alt="car-forbidden" /> استعلام خلافی { inquiry.type == "car" ? "خودرو" : "موتور" } {inquiry.detail ? "با جزییات" : "بدون جزییات" }</h3>
                <div className="flex max-md:flex-wrap max-md:flex-col-reverse max-md:flex-reverse md:justify-between max-md:order-3  max-md:pt-1.5">
                    <h4 className="max-md:w-1/2">
                        {
                            inquiry.type === "car" ?
                            (
                                <>
                                    <span> {right} </span>
                                    <span> {mid} </span>
                                    <span> {alphabet} </span>
                                    <span> {left} </span>
                                    <span>ایران</span>                                
                                </>
                            ) : 
                            (
                                <>
                                    <span> {right} </span>
                                    <span> {left} </span>
                                    <span>ایران</span>                                
                                </>
                            )
                        }
                    </h4>
                    <span className="max-md:w-1/2 max-md:py-1 whitespace-nowrap">
                        تاریخ استعلام {inquiry.created_at} 
                    </span>
                    {/* <p className=" max-md:w-full">
                        جمع خلافی: <span>150,000</span> تومان
                    </p> */}
                </div>
            </div>
        </div>
        <div>
            <span  className="flex items-center gap-2 bg-gray-50 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">
                <EyeIcon />
            </span>
        </div>
    </Link>
  )
}

export default InquiryItem