import DashboardNavbar from "@/components/Header/DashboardNavbar";
import Sidebar from '@/components/Header/Sidebar'
import type{ ReactNode } from "react";


type Props = {
    children: ReactNode;
}

export default function layout({children}: Props) {
    return (
        <div className='flex items-start gap-8 md:p-3 mb-10'>
            <div className='hidden lg:block h-full'>
                <Sidebar />
            </div>

            <div className='w-full flex flex-col gap-6 md:p-3'>
                <DashboardNavbar />
                {children}
                {/* <div className=' flex flex-col gap-4 h-full'>
                    <UserQuickAccess inquirieslength={inquiries.length} />
                    <div className=' grid lg:grid-cols-4 md:grid-cols-1 max-md:grid-cols-1 md:gap-y-4 lg:gap-4 max-md:gap-4'>
                        <UserLastInquiries inquiries={inquiries} error={response.message} />
                        <div className='w-full flex flex-col gap-4 max-md:px-3 h-full '>
                            <WalletAmount />
                            <PopularServices />
                        </div>
                    </div>
                </div> */}
                {/* <LastOrders /> */}
                {/* <FavoritesList /> */}
            </div>
        </div>
    );
}
