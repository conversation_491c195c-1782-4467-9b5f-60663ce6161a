import PriceRange from './PriceRange'
import AvailableProductsFilter from './AvailableProductsFilter'
import WarrantyFilter from './WarrantyFilter'
import SelectColor from './SelectColor'
import ConditionalFilter from './ConditionalFilter'
import {Dispatch, SetStateAction} from "react";
import {ProductFilterOptions} from "@/lib/types/product.types";
import CustomButton from "@/components/UI/CustomButton";
import {CategorySearchableAttributes} from "@/lib/types/category.types";
import DynamicAttributeFilters from "@/components/shop/mainPage/DynamicAttributeFilters";

type Props = {
    productParams: ProductFilterOptions
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>
    minPrice: number,
    maxPrice: number,
    onFilterReset?: () => void;
    onClose?: () => void;
    colors?: CategorySearchableAttributes
    filters?: CategorySearchableAttributes[]

}

const MobileFilterDrawer = ({
                                setProductParams,
                                productParams,
                                minPrice,
                                maxPrice,
                                onFilterReset,
                                onClose,
                                colors,
                                filters,
                            }: Props) => {
    return (
        <>
            <div className="fixed inset-0 z-50 mb-[100px] bg-white flex flex-col">
                {/* Scrollable content */}
                <div className="flex-1 overflow-y-auto  px-3 pb-10 pt-5">
                    <PriceRange
                        min={minPrice || 0}
                        max={maxPrice || 10000}
                        maxRange={productParams?.max_price || maxPrice || 10000}
                        minRange={productParams?.min_price || minPrice || 0}
                        setProductParams={setProductParams}
                    />
                    {/*<div className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}

                    {/*<div className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}
                    <DynamicAttributeFilters
                        filters={filters || []}
                        productParams={productParams}
                        setProductParams={setProductParams}
                    />

                    {/*<div className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}

                    <SelectColor
                        color={productParams.attribute_color}
                        onChange={(value: string) => {
                            setProductParams?.((prevState) =>
                                ({...prevState, attribute_color: value, page: 1}));
                        }}
                        colors={colors}
                    />
                    {/*<div*/}
                    {/*    className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}

                    <ConditionalFilter/>

                    {/*<div className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}

                    <AvailableProductsFilter
                        setProductParams={setProductParams}
                        value={productParams.in_stock_only}
                    />
                    {/*<div className="border-b border-gray-100 my-5 w-[70%] mx-auto">*/}
                    {/*</div>*/}

                    <WarrantyFilter
                        setProductParams={setProductParams}
                        value={productParams.has_guarantee_only}
                    />
                </div>
            </div>
            <div
                onClick={(e) => e.stopPropagation()}
                className='fixed bottom-0 left-0 right-0 h-[100px] w-full bg-white border-t border-gray-200 px-5 z-[1000] md:hidden'>
                <div className="w-full h-full flex justify-between items-center gap-x-5">
                    <CustomButton onClick={() => onClose?.()}>مشاهده محصولات</CustomButton>
                    <CustomButton variant='gray' onClick={(e) => {
                        onFilterReset?.();
                    }}>حذف فیلترها</CustomButton>
                </div>
            </div>
        </>
    );
};


export default MobileFilterDrawer