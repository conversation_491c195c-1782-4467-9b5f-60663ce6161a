// import AddToCartBox from "@/components/category/ProductPage/AddToCartBox"
import OtherCustomers from "@/components/shop/ProductPage/OtherCustomersList"
import ProductCartDetails from "@/components/shop/ProductPage/ProductCartDetails"
import ProductDescription from "@/components/shop/ProductPage/ProductDescription"
import ProductHelp from "@/components/shop/ProductPage/ProductHelp"
import ProductInfo from "@/components/shop/ProductPage/ProductInfo"
import ProductTabs from "@/components/shop/ProductPage/ProductTabs"
import QuestionsAndAnswers from "@/components/shop/ProductPage/QuestionsAndAnswers"
import RecommendedProductsSlider from "@/components/shop/ProductPage/RecommendedProductsSlider"
import ShopAssurances from "@/components/shop/ProductPage/ShopAssurances"
import UserComments from "@/components/shop/ProductPage/UserComments"
import {
    getProduct,
    getProductHelp,
    getProductComments,
    getProductQuestions
} from "../../../../lib/services/productService"

const ProductPage = async ({params}: { params: Promise<{ slug: string }>; }) => {
    const {slug} = await params

    // Fetch all product data using our service functions
    const response = await getProduct(slug);
    console.log(response);
    const productData = response?.data;
    console.log(productData);
    

    const productHelp = await getProductHelp(slug);
    const productComments = await getProductComments(slug);
    const productQuestions = await getProductQuestions(slug);
    // productData.
    return (
        <div className="container mx-auto max-md:px-3">
            <ProductCartDetails productData={productData}/>
            <ShopAssurances/>
            {/* <OtherCustomers/> */}
            <div className="flex justify-between mb-10">
                <div className="md:w-[70%] max-md:w-[98%] max-md:mx-auto">
                    <ProductTabs/>
                    <ProductDescription/>
                    <ProductInfo productDetails={productData.details}/>
                    <ProductHelp productHelp={productHelp}/>
                    <UserComments contentId={productData.id} productComments={productComments.data}/>
                    <QuestionsAndAnswers questions={productQuestions.data}/>
                </div>
                {/* <AddToCartBox /> */}
            </div>
            <RecommendedProductsSlider slug={slug}/>
        </div>
    )
}

export default ProductPage