"use server"


import { apiClient } from "@/lib/apiClient";
import CookieService from "@/lib/services/cookie-service";
import { getUserAddresses } from "@/lib/utils";
// import { cookies } from "next/headers";

export const getServicesAction = async () => {
    const response = await (await apiClient(`services`, {
        next: { revalidate: 900 }
    })).json()
    return response

}

export async function SetNaghlieCookie(token: string) {
    try {
        console.log('this is runnin');

        await CookieService.setAuthorizationToken(token)



    } catch (error) {
        console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');

        console.log(error);


    }
    // const {setAuthorizationToken} = CookieService

}

/**
 * Send notification token to the server
 * @param token Firebase notification token
 * @param path Current path when the token was generated
 * @param status Accept or reject status
 * @param userAgent User's browser and device information
 * @param ip User's IP address (optional, will be determined server-side if not provided)
 * @returns Response from the API
 */
export async function sendNotificationToken(
    token: string,
    path: string,
    status: string,
    userAgent: string = '',
    ip: string = ''
) {
    try {

        const response = await apiClient("notification/client-token", {
            method: "POST",
            body: {
                token,
                path,
                app: "khodrox",
                status,
                agent: userAgent,
                ip
            }
        });

        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error sending notification token:", error);
        return { success: false, error: "Failed to send notification token" };
    }
}

export async function getProducts(productFilter: ProductFilterOptions): Promise<GenericResponse<ProductResponseData>> {
    const {has_guarantee_only, in_stock_only, page = 1, search, sort, min_price, max_price, limit = 9} = productFilter;
    const url = toQueryParams({
        page,
        limit,
        search,
        sort,
        min_price,
        max_price,
        in_stock_only,
        has_guarantee_only,
    });
    // const env = envConfig();
    // const baseUrl = process.env.BASE_URL_2;
    // console.log(env, '<<<<<<<<<<<<<<<<<<<<<<');
    // const res = await fetch(`${baseUrl}products${url ? '?' + url : ''}`, {
    //     method: 'GET',
    //     headers: {
    //         'Content-Type': 'application/json',
            
    //     },
    // });
    const res = await apiClient(`products${url ? '?' + url : ''}`, {
        base: "alt",
        method: "GET",
    })
    
    if (!res.ok) {
        throw new Error(`Failed to fetch product: ${res.status} ${res.statusText}`);
    }
    
    const data = await res.json();
    console.log("-------------------",data);
    return data;
}