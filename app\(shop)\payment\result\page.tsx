import { warn } from "console"
import Container from "@/components/common/Container"
import { apiClient } from "@/lib/apiClient"
import PaymentRedirect from "@/components/shop/paymentResult/PaymentRedirect"

type PageProps = {
  searchParams: Promise<{
    Authority?: string
    Status?: string
  }>
}

const page = async ({searchParams}: PageProps) => {
    const { Authority, Status } = await searchParams
    //  const statusResponse = await fetch(`https://shop-khodrox.liara.run/api/v1/verify-transaction`, {
    //     method: "POST",
    //     headers: {
    //         "Authorization": "matin_token",
    //         "Content-Type": "application/json",
    //         "X-Application-Token": "matin_token",
    //         "Accept": "application/json"
    //     },
    //     body: JSON.stringify({
    //         authority: Authority,
    //         status: Status
    //     })
    //  })
    //  .then(res => res.json())
    const statusResponse = await apiClient(`verify-transaction`, {
        base: "alt",
        method: "POST",
        body: { authority: Authority, status: Status }
    })
    .then(res => res.json())
     warn(statusResponse)
    return (
        <Container className='!px-0'>
            <PaymentRedirect statusResponse={statusResponse} />
        </Container>

    )
}

export default page