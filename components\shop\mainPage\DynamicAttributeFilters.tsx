'use client';

import React, {Dispatch, SetStateAction} from 'react';
import AttributeFilter from './AttributeFilter';
import {CategorySearchableAttributes} from "@/lib/types/category.types";
import {ProductFilterOptions} from "@/lib/types/product.types";

type Props = {
    filters: CategorySearchableAttributes[];
    productParams: ProductFilterOptions;
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>,
};

const DynamicAttributeFilters = ({filters, productParams, setProductParams}: Props) => {
    if (!filters?.length) return null;

    return (
        <>
            {filters.map((filter) => (
                <AttributeFilter
                    key={filter.english_title}
                    attribute={filter}
                    selectedValue={productParams[`attribute_${filter.english_title}`]}
                    onChange={(key: string, value: string) => {
                        setProductParams?.((prev) => ({
                            ...prev,
                            [key]: value,
                            page: 1, // reset page on filter change
                        }));
                    }}
                />
            ))}
        </>
    );
};

export default DynamicAttributeFilters;