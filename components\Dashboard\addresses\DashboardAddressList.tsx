'use client'

import { useState } from 'react'
import { UserAddress } from '@/lib/types/types'
import { MapPin, Trash2, <PERSON>cilLine, CirclePlus } from 'lucide-react'
import { deleteUserAddressAction, getUserAddressesAction } from '@/actions/userAddress.action'
import dynamic from 'next/dynamic'
import toast from 'react-hot-toast'
const AddressModal = dynamic(() => import('@/components/shop/checkout/AddressModal'), {
    // loading: () => <p>The map is loading</p>,
    ssr: false,
});


//TODO: refactor this component and make each seprates modals component
const DashboardAddressList = ({ addressList }: { addressList: UserAddress[] }) => {
    const [addresses, setAddresses] = useState<UserAddress[]>(addressList)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isAddressModalOpen, setIsAddressModalOpen] = useState(false)
    const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null);
    const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null)
    const [isDeleting, setIsDeleting] = useState(false)

    const handleDeleteClick = (id: string) => {
        setSelectedAddressId(id)
        setIsModalOpen(true)
    }

    const deleteAddress = async (id: string) => {
        setIsDeleting(true)
        const deleteResponse = await deleteUserAddressAction(id)
        if (deleteResponse) {
            setAddresses((prev) => prev.filter((addr) => addr.id !== id))
        }
        setIsDeleting(false)
    }

    const confirmDelete = async () => {
        if (selectedAddressId !== null) {
            await deleteAddress(selectedAddressId)
            setIsModalOpen(false)
            setSelectedAddressId(null)
        }
    }

    const handleAddressListUpdate = async () => {
        
        console.log('Refreshing address list...');
        const updatedAddresses = await getUserAddressesAction();
        if (updatedAddresses.success && updatedAddresses.data) {
            setAddresses(updatedAddresses.data);
            console.log('Address list updated successfully:', updatedAddresses.data.length, 'addresses');
        } else {
            toast.error(updatedAddresses.message || 'خطا در بروزرسانی لیست آدرسها');
        }
        // try {
        // } catch (error) {
        //     console.error('Failed to refresh address list:', error);
        // }
    };
    // const handleEditAddress = (address: UserAddress) => {

    // };

    const handleEditAddress = (id: string) => {
        const addressToEdit = addresses.find(item => item.id === id);
        if (addressToEdit) {
            setEditingAddress(addressToEdit);
            setIsAddressModalOpen(true); // Open the address modal
        }
    };

    return (
        <>
            <div className='flex justify-between items-center'>
                <div className='flex items-center '>
                    <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-3 rounded-b-full'>
                        <MapPin className='w-full h-full'  />
                    </div>
                    <h2 className='mb-0 md:text-lg max-md:text-base'>آدرس های من</h2>
                </div>
                <div>
                    <button onClick={() => setIsAddressModalOpen(true)} className='flex items-center gap-2 max-md:text-xs text-primary border border-gray-100 px-4 py-3 rounded-2xl'>
                        <CirclePlus size={22} /> افزودن آدرس جدید
                    </button>
                </div>
            </div>
            <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                {addresses.length ? (
                    addresses.map((address) => (
                        <div key={address.id} className="group border border-gray-400 group-hover:border-primary p-5 rounded-3xl transition-colors duration-300 hover:border-primary">
                            <div className="flex gap-3">
                                <div>
                                    <MapPin className="transition-colors duration-300 group-hover:stroke-primary" size={24} />
                                </div>
                                <div className="flex flex-col gap-3 text-sm">
                                    <p className="md:text-lg transition-colors duration-300 group-hover:text-primary">{address.address}</p>
                                    <div className="text-xs text-gray-400 flex flex-col gap-2">
                                        <p>کد پستی: <span>{address.zip_code}</span></p>
                                        <p>گیرنده: <span>{address.receiver_name}</span></p>
                                    </div>
                                    <div className="flex gap-3 items-center w-fit">
                                        <button
                                            onClick={() => handleDeleteClick(address.id)}
                                            className="bg-red-500 text-white flex items-center gap-2 px-2 py-2 rounded-lg"
                                        >
                                            <Trash2 size={18} />
                                            حذف
                                        </button>
                                        <button
                                            onClick={() => handleEditAddress(address.id)}
                                            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-400 border bg-gray-50 transition-colors duration-300 group-hover:text-white group-hover:bg-primary"
                                        >
                                            ویرایش <PencilLine size={18} className="transition-colors duration-300 group-hover:stroke-white" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    ))
                ) : (
                    <div className="text-gray-400 mt-5">
                        <h2>آدرسی برای نمایش وجود ندارد</h2>
                    </div>
                )}

            </div>


            <AddressModal
                open={isAddressModalOpen}
                onClose={() => {
                    setIsAddressModalOpen(false);
                    setEditingAddress(null); // Reset on close
                }}
                addressToEdit={editingAddress}
                onAddressCreated={handleAddressListUpdate}
            />

            {/* Modal */}
            {isModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-xl max-w-sm w-full text-center shadow-xl">
                        <h2 className="text-lg font-bold mb-4">حذف آدرس</h2>
                        <p className="text-sm text-gray-600 mb-6">آیا مطمئن هستید که می‌خواهید این آدرس را حذف کنید؟</p>
                        <div className="flex justify-center gap-4">
                            <button
                                disabled={isDeleting}
                                onClick={() => setIsModalOpen(false)}
                                className="px-4 py-2 rounded-lg border"
                            >
                                لغو
                            </button>
                            <button
                                disabled={isDeleting}
                                onClick={confirmDelete}
                                className="px-4 py-2 rounded-lg bg-red-500 text-white flex items-center gap-2"
                            >
                                {isDeleting ? (
                                    <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                    'تأیید حذف'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default DashboardAddressList
