'use server'

import {
    ActionResult,
    AuthResponse,
    GetUserResponse,
    VerificationArgs,
    VerificationResponse
} from "@/lib/types/action-types";
import { handleActionErrorResponse } from "@/utils/helpers";
import authService from "@/lib/services/auth.service";
import cookieService from "@/lib/services/cookie-service";
import CookieService from "@/lib/services/cookie-service";
import { apiClient } from "@/lib/apiClient";
import { SetNaghlieCookie } from "./other.action";
import { AuthParams } from "@/app/login/page";

export async function authOTP(phone: string): Promise<ActionResult<AuthResponse>> {
    try {
        const responseResult = await authService.auth<AuthResponse>({ payload: { phone } })
        return {
            success: true,
            data: responseResult.data,
            message: responseResult.data?.message
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function authVerification(args: VerificationArgs): Promise<ActionResult<VerificationResponse>> {
    try {
        const responseResult = await authService.verification<VerificationResponse>({ payload: args })

        const token = `${responseResult.data.token_type} ${responseResult.data.access_token}`
        const decodedToken = authService.decodeJwtToken(token)
        console.log("***************************", decodedToken);

        await CookieService.setAuthorizationToken(token, decodedToken.exp)
        return {
            success: true,
            data: responseResult.data,
            message: responseResult.data?.message
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getUser(): Promise<ActionResult<GetUserResponse>> {
    try {
        const responseResult = await authService.getUser<GetUserResponse>();
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function logOut(): Promise<ActionResult<GetUserResponse>> {
    try {
        await cookieService.deleteAuthorizationToken()
        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}
export async function logginUserFromKhalafiyar(params: AuthParams) {
    try {
        const response = await apiClient("auth/deposit/transfer", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: {
                // userId: params.userId || null,
                type: params.type,
                token: params.token || null,
                phone: params.phone || null,
                withDetails: Boolean(params.withDetails) || false,
                details : {
                    plaque: {
                        plateLeft: params.plateLeft || null,
                        plateMiddle: params.plateMiddle || null,
                        plateRight: params.plateRight || null,
                        plateAlphabet: params.plateAlphabet || null
                    },
                    phoneNumber: params.phoneNumber || null,
                    nationalCode: params.nationalCode || null,
                }

            }
        })
        console.log(params);
        


        const data = await response.json()


        // const contentType = response.headers.get('content-type')
        // if (!contentType || !contentType.includes('application/json')) {
        //     const text = await response.text()
        //     throw new Error(`Expected JSON but got: ${text.substring(0, 100)}...`)
        // }
        // if (!response.ok) {
        //     throw new Error(data.message || 'API request failed')
        // }

        if (data?.data?.access_token) {
            // await SetNaghlieCookie(`Bearer ${data.data.access_token}`)
             await CookieService.setAuthorizationToken(`Bearer ${data.data.access_token}`)

        }

        return data
    } catch (error) {
        console.error("Error logging in user from Khalafiyar:", error);
        return { success: false, error: "Failed to log in user from Khalafiyar" };
    }
}
