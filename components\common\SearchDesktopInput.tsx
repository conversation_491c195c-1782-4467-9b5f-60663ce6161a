"use client"

import React, {useState, useRef, useEffect, Dispatch, SetStateAction, useMemo, ChangeEvent} from "react"
import {Search, X} from "lucide-react"
import {ProductFilterOptions} from "@/lib/types/product.types"
import debounce from "lodash/debounce";
import {useRouter} from "next/navigation";

type Props = {
    value: string
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>
    children: React.ReactNode
}

export function SearchDesktopInput({value, setProductParams, children}: Props) {
    const [isFocused, setIsFocused] = useState(false)
    const [searchValue, setSearchValue] = useState("")
    const inputRef = useRef<HTMLInputElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)
    const router = useRouter();

    useEffect(() => {
        setSearchValue(value);
    }, [value]);

    const debouncedSearch = useMemo(() => {
        return debounce((value: string) => {
            setProductParams?.((prevState) => ({
                ...prevState,
                search: value,
                page: 1
            }));
        }, 700);
    }, [setProductParams]);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSearchValue(value);
        debouncedSearch(value);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
                setIsFocused(false)
            }
        }

        document.addEventListener("mousedown", handleClickOutside)
        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [])

    const handleFocus = () => {
        setIsFocused(true)
    }

    const handleClear = () => {
        setProductParams?.((prevState) => ({
            ...prevState,
            search: '',
            page: 1
        }));
    }

    const handleLinkClick = () => {
        router.push(`/category?search=${encodeURIComponent(searchValue.trim())}`);
        inputRef.current?.blur()
        setIsFocused(false)
    }

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            router.push(`/category?search=${encodeURIComponent(searchValue.trim())}`);
            inputRef.current?.blur()
            setIsFocused(false)
        }
    };


    return (
        <div className="w-full max-w-4xl mx-auto p-4">
            <div
                ref={containerRef}
                className={`relative transition-all duration-200 ${
                    isFocused ? "bg-white shadow-lg rounded-full" : "bg-gray-100 rounded-full"
                }`}
            >
                {/* Search Input */}
                <div className="w-full relative">
                    <input
                        ref={inputRef}
                        type="text"
                        value={searchValue}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        onFocus={handleFocus}
                        placeholder="جستجو"
                        className="w-full py-3 pl-4 pr-10 text-gray-500 bg-transparent rounded-full outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                    />
                    <div
                        onClick={handleLinkClick}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 cursor-pointer">
                        <Search className="w-5 h-5 text-gray-400"/>
                    </div>

                    {searchValue && (
                        <button
                            className="absolute right-4 top-1/2 transform -translate-y-1/2"
                        >
                            <X
                                onClick={handleClear}
                                className="w-5 h-5 text-gray-400 hover:text-gray-600"/>
                        </button>
                    )}
                </div>

                {/* Suggestions Dropdown */}
                {isFocused && value && (
                    <div
                        className="absolute top-full left-0 right-0 mt-1 border bg-white shadow-lg max-h-96 min-h-fit pb-5 z-50 overflow-y-auto rounded-b-lg">
                        {children}
                    </div>
                )}
            </div>
        </div>
    )
}
