"use client"
import SpecialPrice from "@/components/common/SpecialPrice"
import { CartApiItem, useCart } from "@/lib/context/cart-context"
import { Plus, Trash2 } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

const BasketCartItem = ({ product }: { product: CartApiItem }) => {
    console.warn(product);
    const { checkoutAddToCart, checkoutDecreaseFromCart, isLoading } = useCart();
    const [updatingButton, setUpdatingButton] = useState<'add' | 'decrease' | null>(null);

    const handleAddToCart = async () => {
        if (product) {
            setUpdatingButton('add');
            await checkoutAddToCart({ id: product.id || "", quantity: 1 });
            setUpdatingButton(null);
        }
    };

    // Decrease quantity or remove the selected variant from cart
    const handleDecreaseFromCart = async () => {
        if (product) {
            setUpdatingButton("decrease")
            await checkoutDecreaseFromCart(product.id || "")
            setUpdatingButton(null)
        }
    };
    return (
        <div className="body mt-5 flex max-md:flex-col md:justify-between md:px-3 md:items-center max-md:gap-4">
            <div className='flex items-center gap-5'>
                <div className='relative w-32 h-20 px-3.5 max-md:w-24 border-[2px] border-dashed bg-gray-50 rounded-3xl'>
                    <Image fill src={product.image || ""} className='rounded-xl' alt='product-img' />
                </div>
                <div>
                    <h3 className='max-md:text-sm'>
                        {product.name}
                    </h3>
                    <div className="flex gap-4">
                        {product.attributes?.map((attribute, index) => (
                            <div key={index} className="flex gap-2">
                                <span>
                                    {attribute.title}
                                </span>
                                <span className="text-sm font-bold text-gray-700">
                                    {attribute.value}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div>
                <div className='flex max-md:justify-between md:py-2.5 md:px-3 rounded-full gap-7 bg-gradient-to-l from-gray-100 to-transparent'>
                    <div className='bg-white max-md:border flex items-center gap-3 rounded-full p-3 max-md:p-1.5 md:w-32'>
                        <div className="bg-gray-400 rounded-full p-1">
                            <button onClick={handleAddToCart} className="w-3.5 h-3.5 md:w-6 md:h-6 flex items-center justify-center" disabled={product.quantity === product.current_quantity}>

                                {updatingButton === 'add' ? (
                                    <div className="w-4 h-4 md:w-6 md:h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <Plus size={16} color="white" />
                                )}
                            </button>
                        </div>
                        <span className="text-lg">{product.quantity}</span>
                        <button onClick={handleDecreaseFromCart}>
                            {
                                updatingButton === "decrease" ?
                                    <div className="w-4 h-4 md:w-6 md:h-6 border-2 border-red-400 border-t-transparent rounded-full animate-spin"></div>
                                    :
                                    <Trash2 color="red" className="w-4 h-4 md:w-6 md:h-6" />
                            }
                        </button>
                    </div>
                    <div className='flex md:flex-col max-md:items-center max-md:!text-sm max-md:gap-3'>
                        {
                            product.sale_price ?
                                <>
                                    <SpecialPrice price={product.price} className='text-base max-md:!text-xs' />
                                    <span className='text-red-400 font-bold md:text-xl max-md:text-sm'>{product.sale_price.toLocaleString()} تومان</span>
                                </>
                                :
                                <span className="md:text-xl max-md:text-sm">{product.price.toLocaleString()} تومان</span>
                        }

                    </div>
                </div>

            </div>
        </div>
    )
}

export default BasketCartItem