import Image from "next/image";
import BlogPic1 from "@/public/assets/images/blog-pic1.png";
import Link from "next/link"; 
interface RecommendedPostProps {
  image?: string;
  title?: string;
  className?: string;
  url: string
}

const RecommendedPost = ({
  image,
  title = "لورم ایپسوم متن ساختگی تیم طراحی سپهر پلاس برای قسمت عنوان وبلاگ",
  className = "",
  url
}: RecommendedPostProps) => {
  return (
    <section>
      <h3 className="sidebar-title relative px-5 mt-5">
        شاید براتون مفید باشه
      </h3>
      <Link href={url}>
        <div className={`relative mx-auto w-full max-md:max-w-[100%] mt-4 h-[200px] rounded-xl overflow-hidden ${className}`}>
          <Image
            src={image || ""}
            alt=""
            fill
            className="object-cover"
            priority
          />
          <h4 className="absolute text-white z-[2] bottom-3 pr-3 leading-8 font-bold top-2">
            {title}
          </h4>
          <div className="bg-gradient-to-t from-black to-transparent opacity-60 absolute top-0 bottom-0 left-0 right-0"></div>
        </div>
      </Link>

    </section>
  );
};

export default RecommendedPost;
