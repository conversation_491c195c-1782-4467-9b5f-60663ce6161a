'use client'

import { useState } from 'react'
import Image from 'next/image'
import ProductImage from "@/public/assets/images/product1.png"
import BoxDone from '@/components/common/svg/BoxDone'
import SpecialPrice from '@/components/common/SpecialPrice'
import ProductSelectorCounter from './ProductSelectorCounter'
import { ChevronUpIcon } from 'lucide-react'
import Subtract from "@/public/assets/images/subtract-border-gray.svg"

const AddToCartBox = () => {
  const [isOpen, setIsOpen] = useState(true)

  return (
    <div className="bg-white w-[27%] h-fit relative rounded-3xl px-3 py-5 transition-all duration-300 max-md:hidden">
      {/* Toggle Button */}
      <div className='absolute rotate-180 -top-3 right-[25%] w-44'>
        <Image src={Subtract} alt='Subtract' className='w-full' />
        <div className='absolute top-2 right-[37%]'>
          <button
            type="button"
            onClick={() => setIsOpen(prev => !prev)}
            className="bg-yellow flex justify-center items-center rounded-full w-11 h-11 p-2"
          >
            <ChevronUpIcon
              color='white'
              className={`transition-transform duration-300 ${!isOpen ? 'rotate-180' : ''}`}
            />
          </button>
        </div>
      </div>

      {/* Collapsible Content */}
      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out ${isOpen ? 'max-h-[1000px]' : 'max-h-12'}`}
      >
        <div className='flex items-center mt-12 gap-5'>
          <div className='max-w-20 border border-dashed rounded-2xl'>
            <Image src={ProductImage} alt='product-image' className='w-full' />
          </div>
          <h3>اسم محصول</h3>
        </div>
        <div className='mt-5 flex items-center gap-3 text-sm px-1'>
          <BoxDone size='20' /> <span>سپهر پلاس</span>
        </div>
        <div className="flex items-center gap-5 mt-5 px-3">
          <p>
            <strong>8,087,000</strong>
            <span>تومان</span>
          </p>
          <div className="relative">
            <SpecialPrice price={8233000} />
            <span className="bg-red-500 text-white p-[2px] text-xs rounded-bl-full rounded-tl-full rounded-tr-full absolute left-5 -top-5">10%</span>
          </div>
        </div>
        <div className='mt-6'>
          <ProductSelectorCounter />
        </div>
        {/* Optional Button */}
        {/* <div>
          <CustomButton className='py-5 flex items-center mt-5'> <ShoppingBasket /> افزودن به سبد خرید </CustomButton>
        </div> */}
      </div>
    </div>
  )
}

export default AddToCartBox
