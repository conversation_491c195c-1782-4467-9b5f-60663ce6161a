export interface createProductCommentResponse {
  success: true;
  message: string;
  status: number;
  data: {
    id: string;
    body: string;
    rate: number;
    has_bought: boolean;
    created_at: string;
    status: 'pending' | 'approved' | 'rejected'; // adjust if more statuses exist
  };
}
export interface createProductCommentPayload {
  id: string;
  body: string;
  rate: number;
  reply_to: string | null; // or string if you prefer empty string over null
}
