import {useEffect, useState} from "react";

export function useIsMobile(breakpoint = 768) {
    const [isMobile, setIsMobile] = useState<'mobile' | 'desktop' | 'pending'>('pending');

    useEffect(() => {
        const checkScreen = () => setIsMobile(window.innerWidth < breakpoint ? 'mobile' : 'desktop');
        checkScreen();
        window.addEventListener("resize", checkScreen);
        return () => window.removeEventListener("resize", checkScreen);
    }, [breakpoint]);

    return isMobile;
}