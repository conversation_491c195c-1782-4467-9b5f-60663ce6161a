import React from "react";

interface MoreVerticalDotsProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  secondaryOpacity?: number;
}

const MoreVerticalDots: React.FC<MoreVerticalDotsProps> = ({
  color = "#363a3e",
  secondaryOpacity = 0.4,
  ...props
}) => {
  return (
    <svg
     
      viewBox="0 0 12 2.5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.5,1.25A1.25,1.25,0,1,1,1.25,0,1.25,1.25,0,0,1,2.5,1.25Z"
        transform="translate(7.5 0) rotate(90)"
        fill={color}
      />
      <path
        d="M2.5,1.25A1.25,1.25,0,1,1,1.25,0,1.25,1.25,0,0,1,2.5,1.25Z"
        transform="translate(12 0) rotate(90)"
        fill={color}
        opacity={secondaryOpacity}
      />
      <path
        d="M2.5,1.25A1.25,1.25,0,1,1,1.25,0,1.25,1.25,0,0,1,2.5,1.25Z"
        transform="translate(2.5 0) rotate(90)"
        fill={color}
        opacity={secondaryOpacity}
      />
    </svg>
  );
};

export default MoreVerticalDots;
