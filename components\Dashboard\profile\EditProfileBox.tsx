'use client'

import React, { useEffect, useState } from 'react'
import { Camera } from 'lucide-react'
import JalaliDatePicker from '@/components/UI/JalaliDatePicker'
import CustomButton from '@/components/UI/CustomButton'
import { UserProfileData } from '@/lib/types/types'
import { Value } from 'react-multi-date-picker'
import Image from 'next/image'
import { getUserProfile, updateUserProfile } from '@/actions/userProfile.action'
import toast from 'react-hot-toast'

const convertPersianToEnglishNumbers = (input: string): string => {
    const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']

    return input.replace(/[۰-۹]/g, w => englishNumbers[persianNumbers.indexOf(w)])
}


const EditProfileBox = () => {
    const [formData, setFormData] = useState<UserProfileData>({
        full_name: '',
        phone: '',
        email: '',
        national_code: '',
        shamsi_birth_date: '',
        profile_image:''
    })
    const [profileImageFile, setProfileImageFile] = useState<File | null>(null)
    const [isSubmitting, setIsSubmitting] = useState(false) // Loading state for form submission
    const [isLoading, setIsLoading] = useState(true) // Loading state for initial data fetch

    useEffect(() => {
        async function getUser() {
            try {
                setIsLoading(true)
                const user = await getUserProfile()
                console.log(user);

                setFormData({
                    full_name: user?.data?.full_name || '',
                    phone: user?.data?.phone || '',
                    email: user?.data?.email || '',
                    national_code: user?.data?.national_code || '',
                    shamsi_birth_date: user?.data?.shamsi_birth_date || '',
                    profile_image: user?.data?.profile_image || '',
                })
            } catch (error) {
                console.error('Error fetching user profile:', error)
                toast.error("خطا در بارگذاری اطلاعات کاربر")
            } finally {
                setIsLoading(false)
            }
        }
        getUser()

    }, [])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target
        setFormData(prev => ({ ...prev, [id]: value }))
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file) {
            setProfileImageFile(file)
        }
    }

    const handleSubmit = async () => {
        setIsSubmitting(true)
        const form = new FormData()
        
        if (formData.full_name !== undefined) form.append('fullname', formData.full_name)
        if (formData.phone !== undefined) form.append('phone', formData.phone)
        if (formData.email !== undefined) form.append('email', formData.email)
        if (formData.national_code !== undefined) form.append('national_code', formData.national_code)
        if (formData.shamsi_birth_date !== undefined) {
            const englishBirthDate = convertPersianToEnglishNumbers(formData.shamsi_birth_date)
            form.append('shamsi_birth_date', englishBirthDate)
        }
        

        if (profileImageFile) {
            form.append('profile_image', profileImageFile)
        }
        // Debug output
        for (const [key, val] of form.entries()) {
            console.log(`${key}:`, val)
        }

        try {
            const result = await updateUserProfile(form)
            console.log('Server response:', result)
            toast.success(result.message)
        } catch (error) {
            console.error('Submit error:', error)
            toast.error("خطایی رخ داده است")
        } finally {
            setIsSubmitting(false)
        }
    }

    // Show loading spinner while fetching user data
    if (isLoading) {
        return (
            <div className="flex flex-col gap-4 bg-white p-6 rounded-2xl shadow-md min-h-[533px]">
                <div className="flex items-center justify-center h-full min-h-[400px]">
                    <div className="flex flex-col items-center gap-4">
                        <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                        <p className="text-gray-600 text-lg">در حال بارگذاری اطلاعات...</p>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="flex flex-col gap-4 bg-white p-6 rounded-2xl shadow-md min-h-[533px]">
            <div className="md:grid grid-cols-2 gap-x-10 h-full md:w-[90%] max-md:flex max-md:flex-col max-md:gap-4">
                {/* Left form fields */}
                <div className="flex flex-col max-md:gap-4 justify-between">
                    <div className="flex flex-col gap-3">
                        <label htmlFor="full_name">نام و نام خانوادگی</label>
                        <input
                            type="text"
                            id="full_name"
                            value={formData.full_name}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>

                    <div className="flex flex-col gap-3">
                        <label htmlFor="phone">شماره تلفن</label>
                        <input
                            type="text"
                            id="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>

                    <div className="flex flex-col gap-3">
                        <label htmlFor="email">ایمیل</label>
                        <input
                            type="email"
                            id="email"
                            value={formData.email}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>
                </div>

                {/* Profile image upload */}
                <div className="group relative">
                    <input
                        type="file"
                        id="profilePhoto"
                        accept=".jpg,.jpeg,.png"
                        className="hidden"
                        onChange={handleFileChange}
                        disabled={isSubmitting}
                    />
                    <label
                        htmlFor="profilePhoto"
                        className={`flex flex-col justify-center items-center border-2 border-dashed border-gray-300 rounded-3xl p-6 cursor-pointer group-hover:border-primary transition-colors ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                        <div className="p-2 border-dashed border-2 border-gray-300 rounded-full mb-4 group-hover:border-primary transition-colors">
                            <div className="w-28 h-28 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                                {profileImageFile || formData.profile_image ? (
                                    <Image
                                        src={profileImageFile ? URL.createObjectURL(profileImageFile) : formData.profile_image || ""}
                                        alt="preview"
                                        fill
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <Camera stroke="white" size={30} />
                                )}
                            </div>
                        </div>
                        <h3 className="text-center mb-2">عکس پروفایل</h3>
                        <p className="text-sm text-gray-500 mb-1">فرمت مورد قبول: *.jpg, *.png, *.jpeg</p>
                        <p className="text-sm">حداکثر حجم فایل: 3 مگابایت</p>
                    </label>
                </div>
            </div>

            {/* Date + national code */}
            <div className="md:grid grid-cols-2 gap-x-10 h-full md:w-[90%] mt-5 max-md:flex max-md:flex-col max-md:gap-4">
                <div className="flex flex-col gap-3">
                    <label htmlFor="shamsi_birth_date">تاریخ تولد</label>
                    <JalaliDatePicker
                        value={formData.shamsi_birth_date || ''}
                        setValue={(val: Value) =>
                            setFormData(prev => ({ ...prev, shamsi_birth_date: val?.toString?.() ?? '' }))
                        }
                    // disabled={isSubmitting}
                    />
                </div>

                <div className="flex flex-col gap-3">
                    <label htmlFor="national_code">کد ملی</label>
                    <input
                        placeholder="کد ملی"
                        type="text"
                        id="national_code"
                        value={formData.national_code}
                        onChange={handleChange}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        disabled={isSubmitting}
                    />
                </div>
            </div>

            <div>
                <CustomButton
                    className="w-fit px-8 py-4 mt-5"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                >
                    {isSubmitting ? (
                        <div className="flex items-center gap-2">
                            <span>در حال ذخیره...</span>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    ) : (
                        "ویرایش و ذخیره"
                    )}
                </CustomButton>
            </div>
        </div>
    )
}

export default EditProfileBox