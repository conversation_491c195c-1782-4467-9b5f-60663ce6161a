import React, {Dispatch, SetStateAction} from 'react'
import PriceRangeFilter from './PriceRangeFilter'
import AccordionHeader from './AccordionHeader'
import {ProductFilterOptions} from "@/lib/types/product.types";

type Props = {
    min: number,
    max: number,
    minRange?: number,
    maxRange?: number,
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>,
}

const PriceRange = (props: Props) => {
    return (
        <div
            className='bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter'>
            <AccordionHeader title='محدوده قیمت'>
                <PriceRangeFilter {...props} />
            </AccordionHeader>


        </div>
    )
}

export default PriceRange