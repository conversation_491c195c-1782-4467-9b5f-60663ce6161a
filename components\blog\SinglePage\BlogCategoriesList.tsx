import { CateoryResponse } from "@/lib/types/article.types"
import {ChevronLeft} from "lucide-react"
import Link from "next/link"

const BlogCategoriesList = ({categories}: {categories: CateoryResponse}) => {
    return (
        <section className="px-1">
            <h3 className="sidebar-title relative px-5 mb-5 mt-8">
                دسته بندی مطالب
            </h3>
            {/* TODO: this part of code must become dynamic to use in category main page */}
            <ul className='flex flex-col gap-6 mb-3 text-sm mt-5'>
                {
                    categories.data?.map((category,indx) => <Link href={`/blog/${category.slug}`} key={indx}>
                    <li className='flex items-center justify-between relative'>
                        {category.title} ({category.total_articles})
                        <ChevronLeft className='text-gray-400'/>
                    </li>                
                </Link>)
                }
                
                {/* <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما (131)
                    <ChevronLeft className='text-gray-400'/>
                </li>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما (311)
                    <ChevronLeft className='text-gray-400'/>
                </li>
                <li className='flex items-center justify-between relative'>
                    دسته بندی وبلاگ شما (141)
                    <ChevronLeft className='text-gray-400'/>
                </li> */}

            </ul>

        </section>
    )
}

export default BlogCategoriesList