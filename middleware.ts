import { NextRequest, NextResponse } from 'next/server'
import { AUTHORIZATION, RETURN_URL } from "@/lib/constants"
import { DASHBOARD_PATH, HOME_PATH, LOGIN_PATH, protectedRoutes, signOutOnlyRoutes } from "@/lib/routes"
import authService from "@/lib/services/auth.service"
import { NextURL } from "next/dist/server/web/next-url"
import cookieService from "@/lib/services/cookie-service"

// List of parameters that indicate a special cross-website login
// const SPECIAL_LOGIN_PARAMS = [
//   'type',
//   'withDetails',
//   'plateLeft',
//   'plateMiddle',
//   'plateRight',
//   'plateAlphabet',
//   'phoneNumber',
//   'nationalCode',
//   'amount',
//   'userId',
//   'phone'
// ]

export default async function middleware(req: NextRequest) {
  const jwt_token = req.cookies.get(AUTHORIZATION)
  const url = req.nextUrl
  const path = url.pathname

  // Skip middleware for certain paths
  if (path === "/tickets-result") {
    return NextResponse.next()
  }

  // Check if this is a special login case
  // const hasSpecialLoginParams = SPECIAL_LOGIN_PARAMS.some(param => 
  //   url.searchParams.has(param)
  // )

  const isProtectedRoute = protectedRoutes.includes(path) || path.startsWith('/dashboard')
  console.log("is protedcted :",isProtectedRoute);
  
  const isShownOnlyWhenSignOut = signOutOnlyRoutes.includes(path)
  const isAuthorized = !!jwt_token?.value && authService.jwtTokenIsValid(jwt_token.value)

  
  if (jwt_token?.value && !isAuthorized) {
    await cookieService.deleteAuthorizationToken()
  }

  // Special case: If user is coming from another website with login params
  // AND is on the login page, skip all redirects
  if (path === LOGIN_PATH) {
    return NextResponse.next()
  }

  
  if (!isAuthorized && path === DASHBOARD_PATH) {
    const loginUrl = new URL(LOGIN_PATH, url.origin)
    return NextResponse.redirect(loginUrl)
  }

  if (isProtectedRoute && !isAuthorized) {
    return redirectToLogin(url)
  }

  if (isShownOnlyWhenSignOut && isAuthorized) {
    return redirectToHome(url)
  }

  return NextResponse.next()
}

function redirectToHome(url: NextURL) {
  const homeUrl = new URL(HOME_PATH, url.origin)
  return NextResponse.redirect(homeUrl)
}

function redirectToLogin(url: NextURL) {
  const loginUrl = new URL(LOGIN_PATH, url.origin)
  loginUrl.searchParams.set(RETURN_URL, url.pathname + url.search)
  return NextResponse.redirect(loginUrl)
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|.*\\.png$|tickets-result).*)',
  ],
}