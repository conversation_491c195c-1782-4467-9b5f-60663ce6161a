import {ProductFilterOptions} from "@/lib/types/product.types";
import {getProductsByCategory} from "@/lib/services/productService";
import {headers} from "next/headers";
import ShopCategorySlugComponent from "@/components/shop/ShopCategorySlugComponent";
import CategoryService from "@/lib/services/category.service";

type Props = {
    searchParams: Promise<ProductFilterOptions>
    params: Promise<{ categorySlug: string }>
}


export default async function ProductsByCategoryPage({searchParams, params}: Props) {

    const filterParams = await searchParams
    const {
        in_stock_only = 'true',
        page,
        limit = 20
    } = filterParams

    const {categorySlug} = await params

    const userAgent = (await headers()).get('user-agent') || ''
    const isMobile = /mobile/i.test(userAgent)

    const productFilter: ProductFilterOptions = {
        ...filterParams,
        in_stock_only,
        page: page ? page : !isMobile ? 1 : undefined,
        limit,
    }

    let productResponse,
        categoryBreadCrumbResponse,
        categoriesResponse,
        categoryAttributesResponse;

    try {
        [
            productResponse,
            categoryBreadCrumbResponse,
            categoriesResponse,
            categoryAttributesResponse
        ] = await Promise.all([
            getProductsByCategory(categorySlug, productFilter),
            CategoryService.getCategoryBreadCrumb(categorySlug),
            CategoryService.getCategoriesBySlug(categorySlug),
            CategoryService.getCategoryAttributes(categorySlug)
        ]);
    } catch (error) {
        console.error("Error fetching data:", error);
    }

    const categoriesForBreadCrumb = [{slug: "", title: "فروشگاه"}, ...(categoryBreadCrumbResponse?.data || [])]
    const categories = [{slug: "", title: "همه کالاها"}, ...(categoriesResponse?.data || [])]
    const colorFilterResults = (categoryAttributesResponse?.data) ? categoryAttributesResponse?.data.find((c) => c.english_title === 'color') : undefined
    const otherFilterResults = (categoryAttributesResponse?.data) ? categoryAttributesResponse?.data.filter((c) => c.english_title !== 'color') : undefined

    return (
        <>
            <ShopCategorySlugComponent
                params={productFilter}
                data={productResponse}
                isMobile={isMobile}
                categorySlug={categorySlug}
                categoriesForBreadCrumb={categoriesForBreadCrumb}
                categories={categories}
                colors={colorFilterResults}
                filters={otherFilterResults || []}
            />
        </>
    );
}
