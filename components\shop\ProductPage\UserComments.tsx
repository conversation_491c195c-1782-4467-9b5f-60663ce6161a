"use client"
import { MessageCircle } from "lucide-react"
import AccordionWrapper from "./AccordionWrapper"
import CommentTextArea from "./CommentTextArea"

import CommentItem from "./CommentItem"
import { ProductComment } from "@/lib/types/product.types"
import { usePathname, useSearchParams } from "next/navigation"
import { useEffect } from "react"

const UserComments = ({ productComments, contentId }: { productComments: ProductComment[], contentId: string }) => {
    const pathname = usePathname();
    const searchParams = useSearchParams(); // triggers re-render on hash change
    // TODO: later i could move this to a separate empty client component and keep this component ssr
    useEffect(() => {
        const hash = window.location.hash;

        if (hash) {
            const element = document.querySelector(hash);
            if (element) {
                // Scroll after a short delay to ensure DOM is loaded
                setTimeout(() => {
                    element.scrollIntoView({ behavior: "smooth" });
                }, 100);
            }
        }
    }, [pathname, searchParams]);
    return (
        <section className="bg-white rounded-3xl p-5 mt-10 scroll-mt-28" id="productComments">
            <AccordionWrapper title="نظرات مشتریان" icon={<MessageCircle />} roundedArrow={false}>
                <div className="mt-5">
                    <CommentTextArea contentId={contentId} commentType="product" />
                    <div>
                        <h4 className="mb-5">
                            {productComments.length} امتیاز و دیدگاه کاربران
                        </h4>
                        {
                            productComments.length && productComments.map(item => <CommentItem key={item.id} comment={item} />)
                        }
                        {/* <CommentItem /> */}
                        {/* <CommentItem />
                       <CommentItem /> */}
                    </div>
                </div>
            </AccordionWrapper>
        </section>
    )
}

export default UserComments