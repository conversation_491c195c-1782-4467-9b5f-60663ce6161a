"use client"
import Image from 'next/image'
import Ticket from "@/public/assets/images/ticket.png"
import { Info } from 'lucide-react'
import LastInquiriesList from './LastInquiriesList'
import { HistoryTransaction } from '@/lib/types/action-types'
import { useState } from 'react'
export interface UserLastInquiriesProps {
    inquiries: HistoryTransaction[]
    error?: string
    title?: string
}
const UserLastInquiries = ({ inquiries, error, title }: UserLastInquiriesProps) => {
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Calculate the current items to display
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = inquiries?.slice(indexOfFirstItem, indexOfLastItem);

    const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

    return (
        <div className='w-full max-md:w-[95%] max-md:mx-auto relative flex flex-col md:h-full h-[48rem] md:px-10 px-2 py-6 bg-white shadow-md rounded-xl md:col-span-3 max-md:order-4'>
            {/* Header */}
            <div className="flex items-center w-full rounded-lg">
                <div className="flex items-center gap-2 text-gray-900 font-semibold text-lg">
                    <Image src={Ticket} alt='ticket-icon' />
                    <span className='max-md:text-base'>{title}</span>
                </div>
                <div className="flex-1 border-t border-gray-300 max-md:mr-1 md:mx-4"></div>
                <button className="flex items-center gap-2 bg-gray-50 text-gray-600 md:px-4 px-2 py-2 rounded-full text-sm font-medium">
                    <Info />
                </button>
            </div>

            {
                inquiries?.length ?
                <div className="flex-grow overflow-y-auto h-full">
                    <LastInquiriesList inquiries={currentItems} />
                </div>
                :
                <p className='text-right text-red-500 mt-5'> {error} </p>
            }

            <div className="flex justify-center mt-4">
                {Array.from({ length: Math.ceil((Array.isArray(inquiries) ? inquiries.length : 0) / itemsPerPage) }, (_, i) => (
                    <button
                        key={i + 1}
                        onClick={() => paginate(i + 1)}
                        className={`mx-1 px-3 py-1 rounded-full ${currentPage === i + 1 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                    >
                        {i + 1}
                    </button>
                ))}

            </div>
        </div>


    );
}

export default UserLastInquiries