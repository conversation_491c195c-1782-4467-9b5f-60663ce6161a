"use client"

import {ChangeEvent, useEffect, useState} from "react";
import RoundedArrow from "../common/svg/RoundedArrow";
import usePathUrlHelper from "@/lib/hooks/usePathUrlHelper";
import {ViolationQueryParams} from "@/lib/types/types";
import {paymentPost} from "@/actions/payment.action";
import {PAYMENT_RESULT_PATH} from "@/lib/routes";
import toast from "react-hot-toast";
import { useRouter } from 'nextjs-toploader/app';
import CustomButton from "@/components/UI/CustomButton";
import {formatWithComma} from "@/utils/helpers";
import {Minus, Plus} from "lucide-react";

const MIN_AMOUNT = 100_000
const MAX_AMOUNT = 1000_000

const predefinedAmounts = [100000, 200000, 500000];
export default function PaymentBox() {
    const [amount, setAmount] = useState(predefinedAmounts[1]);
    const [errorMessage, setErrorMessage] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [increaseBtnDisabled, setIncreaseBtnDisabled] = useState(false)
    const [decreaseBtnDisabled, setDecreaseBtnDisabled] = useState(false)
    const {getQueryParams} = usePathUrlHelper();
    const [userInquiryInfo, setUserInquiryInfo] = useState<ViolationQueryParams>()
    const router = useRouter()
    const message = `مبلغ وارد شده باید بین ${formatWithComma(MIN_AMOUNT.toString())}  تا ${formatWithComma(MAX_AMOUNT.toString())} تومان باشد `


    const handleAmountChange = (value: number) => {
        if (value < MIN_AMOUNT || value > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        setAmount(value);
    };

    useEffect(() => {
        setDecreaseBtnDisabled(false)
        setIncreaseBtnDisabled(false)
        if (amount <= MIN_AMOUNT) {
            setDecreaseBtnDisabled(true)
        } else if (amount >= MAX_AMOUNT) {
            setIncreaseBtnDisabled(true)
        }
    }, [amount]);

    useEffect(() => {
        const queryParams = getQueryParams<ViolationQueryParams>();
        if (queryParams.inquiry === 'true') {
            setUserInquiryInfo(queryParams)
        }
    }, [])


    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.currentTarget.value.replace(/,/g, ""); // Remove commas
        const numericValue = Number(rawValue);
        if (numericValue < MIN_AMOUNT || numericValue > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        if (!isNaN(numericValue)) {
            setAmount(numericValue);
        }
    };

    async function onPaymentButtonClick() {
        setIsLoading(true)

        if (!amount) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }
        if (amount < MIN_AMOUNT) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }
        if (amount > MAX_AMOUNT) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }

        const actionResult = await paymentPost({
            amount,
            callback_url: `${window.location.origin}${PAYMENT_RESULT_PATH}`,
            type: userInquiryInfo?.isMotor === 'true' ? 'motor' : userInquiryInfo?.isMotor === 'false' ? 'car' : undefined,
            details: userInquiryInfo?.withDetails === 'true' ? {
                phone: userInquiryInfo.phoneNumber!,
                national_id: userInquiryInfo.nationalCode!
            } : undefined,
            plaque: userInquiryInfo?.inquiry ? {
                left: userInquiryInfo.left,
                right: userInquiryInfo.right,
                alphabet: userInquiryInfo?.alphabet,
                mid: userInquiryInfo?.middle
            } : undefined
        })

        if (!actionResult.success) {
            toast.error(actionResult.message!)
            setIsLoading(false)
            return;
        }
        router.replace(actionResult.data!.payment_url)
    }


    return (
        <>
            {userInquiryInfo?.message &&
                <div className="wallet-alert bg-yellow/10 p-5 rounded-xl border-2 border-dashed border-yellow">
                    <p className='text-destructive text-sm font-semibold'>{userInquiryInfo?.message}</p>
                </div>}

            {/* Main content with bottom padding for sticky button on mobile */}
            <div className="flex flex-col items-center bg-white md:p-6 rounded-lg w-full mx-auto rtl mt-5  md:pb-6">

                <div className="text-right w-full pb-2 p-3">
                    <h2 className="increase-amount flex text-lg font-semibold text-gray-800 gap-2">
                        افزایش اعتبار
                        <RoundedArrow/>
                    </h2>
                    <p className="text-gray-500 text-sm mt-1">مبلغ مورد نظر خود را برای افزایش اعتبار انتخاب کنید</p>

                </div>
                <div className="flex gap-2 w-full my-4">
                    {predefinedAmounts.map((amt) => (
                        <button
                            key={amt}
                            className={`whitespace-nowrap border-2 border-dashed  md:px-4 py-4 rounded-3xl text-gray-700 w-full font-bold  transition-all ${amount === amt ? "border-green-400 bg-[#F6FFF9]" : "border-gray-300"
                            }`}
                            onClick={() => handleAmountChange(amt)}
                        >
                            <span className='text-[#596068]'>{amt.toLocaleString()}</span> <span
                            className='text-xs text-[#596068]'>تومان</span>
                        </button>
                    ))}
                </div>

                <div
                    className={`flex items-center border ${errorMessage ? "border-red-500" : "border-gray-300"} rounded-2xl px-5 py-3 w-full`}>
                    <button
                        className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                        onClick={() => handleAmountChange(amount + 10000)}
                        disabled={increaseBtnDisabled}
                    >
                        <Plus size={16} color="white"/>
                    </button>
                    <input
                        type="tel"
                        dir="rtl"
                        className="left-direction text-center w-full bg-transparent text-lg font-semibold focus:outline-none"
                        value={amount.toLocaleString()}
                        maxLength={8}
                        onChange={handleInputChange}
                    />

                    <button
                        className=" bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                        onClick={() => handleAmountChange(amount - 10000)}
                        disabled={decreaseBtnDisabled}
                    >
                        <Minus size={16} color="white"/>
                    </button>
                </div>

                {errorMessage && <p className="text-red-500 text-sm mt-2">{errorMessage}</p>}

                {/* Button for desktop - hidden on mobile */}
                <CustomButton
                    className='py-6 mt-4 hidden md:block'
                    loading={isLoading}
                    disabled={isLoading || !!errorMessage}
                    onClick={onPaymentButtonClick}
                >
                   تایید و پرداخت
                </CustomButton>
            </div>

            {/* Sticky button for mobile - hidden on desktop */}
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 md:hidden z-50">
                <CustomButton
                    className='py-6 w-full'
                    loading={isLoading}
                    disabled={isLoading || !!errorMessage}
                    onClick={onPaymentButtonClick}
                >
                   تایید و پرداخت
                </CustomButton>
            </div>
        </>
    );
}
