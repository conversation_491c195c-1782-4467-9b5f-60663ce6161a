"use client"
import UserAddresses from './UserAddresses'
import FactorCard from './FactorCard'
import { UserAddressesResponse, UserAddress } from '@/lib/types/types'
import { useState } from 'react'
import { getUserAddressesAction } from '@/actions/userAddress.action'

const ShippingPageClient = ({ userAddresses }: { userAddresses: UserAddressesResponse }) => {
    const [selectedAddress, setSelectedAddress] = useState<string>("");
    const [addressList, setAddressList] = useState<UserAddress[]>(userAddresses.data);
    const [isNavigating, setIsNavigating] = useState(false);

    /**
     * Refresh address list from server after a new address is created
     */
    const handleAddressListUpdate = async () => {
        try {
            console.log('Refreshing address list...');
            const updatedAddresses = await getUserAddressesAction();
            if (updatedAddresses.success && updatedAddresses.data) {
                setAddressList(updatedAddresses.data);
                console.log('Address list updated successfully:', updatedAddresses.data.length, 'addresses');
            } else {
                console.error('Failed to fetch updated addresses:', updatedAddresses);
            }
        } catch (error) {
            console.error('Failed to refresh address list:', error);
        }
    };

    return (
        <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>

            <div className='md:w-[70%]'>
                <UserAddresses
                    selectedAddress={selectedAddress}
                    setSelectedAddress={setSelectedAddress}
                    addressList={addressList}
                    onAddressListUpdate={handleAddressListUpdate}
                />
            </div>
            <FactorCard
                selectedAddress={selectedAddress}
                steps={{title: "shipping", nextStepBtnTitle: "ثبت آدرس", nextStepBtnLink: `/checkout/payment?address=${selectedAddress}`}}
                isNavigating={isNavigating}
                setIsNavigating={setIsNavigating}
            />
        </div>
    )
}

export default ShippingPageClient